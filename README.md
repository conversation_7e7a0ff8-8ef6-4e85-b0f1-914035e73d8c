# 🏢 Syndic Management System

A complete **bilingual** (French/Arabic) web application for managing residential co-ownership funds (Syndic) with modern Neumorphism UI design.

## ✨ Features

### 👤 Owner Management
- Add, edit, and delete property owners
- Store complete owner details (name, group, building, apartment, title number)
- Track owner status (active/inactive)
- Support for groups OR2-OR5 and GH14-GH15

### 💰 Income Management
- Record monthly and yearly contributions
- Generate professional payment receipts
- Multiple payment methods (cash, transfer, cheque)
- Track payments from 2018 to 2032
- Automatic receipt numbering

### 🧾 Expense Management
- Record expenses by category (maintenance, electricity, water, security, cleaning)
- Attach invoice files
- Track expenses with detailed descriptions
- Calculate current balance per building

### 📊 Reporting System
- Owner account statements
- Payment reports (daily, monthly, annual)
- Expense analysis reports
- Overdue payments tracking
- Maintenance work narratives
- Export to PDF and Excel formats

### ⚖️ Legal Documents
- Payment notices
- Ownership certificates
- Court performance orders
- Precautionary seizure orders
- Judgment registrations

### 🔔 Notifications
- Overdue payment alerts
- General assembly reminders
- Real-time notification system

### 🌐 Multilingual Support
- Complete French and Arabic translations
- RTL (Right-to-Left) layout for Arabic
- Language switching with persistent preference

## 🎨 Design Features

### Neumorphism UI
- Soft shadows and subtle gradients
- Modern, clean aesthetic
- Smooth 300ms transitions
- Harmonious color palette

### Responsive Design
- Mobile-first approach
- Tablet and desktop optimized
- Flexible grid layouts
- Touch-friendly interface

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server required - runs completely offline

### Installation
1. Download or clone the project files
2. Open `index.html` in your web browser
3. The application will load with an empty database

### Loading Demo Data
For testing purposes, demo data is available:
1. Open the application in a local environment
2. Click the "Charger Données Demo" button (bottom-left corner)
3. Confirm to load sample owners, payments, and expenses

## 📁 Project Structure

```
syndic-management/
├── index.html              # Main application file
├── css/
│   └── styles.css          # Neumorphism styles and responsive design
├── js/
│   ├── app.js              # Main application logic
│   ├── storage.js          # localStorage management
│   ├── translations.js     # Bilingual support system
│   ├── reports.js          # PDF/Excel generation
│   └── demo-data.js        # Sample data for testing
└── README.md               # This file
```

## 🛠️ Technical Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Storage**: localStorage (offline persistence)
- **Styling**: Custom Neumorphism CSS with CSS Grid/Flexbox
- **Icons**: Font Awesome 6
- **Fonts**: Inter (Latin), Noto Sans Arabic (Arabic)
- **PDF Export**: jsPDF library
- **Excel Export**: SheetJS library

## 💾 Data Storage

All data is stored locally in the browser using localStorage:
- **Owners**: Complete owner information and status
- **Payments**: Payment records with receipt numbers
- **Expenses**: Expense tracking with categories
- **Documents**: Legal document metadata
- **Settings**: Application configuration

## 🔧 Key Functions

### Owner Management
```javascript
// Add new owner
const owner = {
    fullName: "Ahmed Ben Ali",
    group: "OR2",
    building: "A",
    apartment: "101",
    titleNumber: "TF-12345/A"
};
storageManager.addOwner(owner);
```

### Payment Recording
```javascript
// Record payment
const payment = {
    ownerId: "owner_id",
    amount: 1200,
    date: "2024-01-15",
    year: 2024,
    paymentMethod: "cash"
};
storageManager.addPayment(payment);
```

### Receipt Generation
```javascript
// Generate and print receipt
const payment = storageManager.getPaymentById(paymentId);
reportsManager.printReceipt(payment);
```

## 🌍 Language Support

The application supports:
- **French** (default): Complete interface translation
- **Arabic**: Full RTL support with Arabic translations
- **Language switching**: Persistent user preference

### Adding New Languages
1. Add translations to `js/translations.js`
2. Update the language toggle functionality
3. Add appropriate fonts if needed

## 📱 Mobile Responsiveness

- **Breakpoints**: 768px for mobile/tablet transition
- **Navigation**: Collapsible menu on mobile
- **Tables**: Horizontal scroll on small screens
- **Forms**: Stacked layout on mobile
- **Touch**: Optimized button sizes and spacing

## 🔒 Security & Privacy

- **Offline-first**: No data sent to external servers
- **Local storage**: All data remains on user's device
- **No tracking**: No analytics or external dependencies
- **Privacy-focused**: Complete data ownership

## 🎯 Usage Examples

### Daily Operations
1. **Record Payment**: Income → Add Payment → Fill details → Save
2. **Add Expense**: Expenses → Add Expense → Select category → Save
3. **Generate Receipt**: Income → Find payment → Print Receipt
4. **Check Overdue**: Dashboard → View overdue payments

### Monthly Tasks
1. **Generate Reports**: Reports → Select report type → Generate
2. **Review Expenses**: Expenses → Filter by month → Analyze
3. **Owner Statements**: Reports → Owner Statement → Select owner

### Annual Operations
1. **Year-end Reports**: Reports → Annual Report → Export
2. **Balance Analysis**: Reports → Balance Report → Review
3. **Data Backup**: Export all data for archival

## 🔧 Customization

### Styling
- Modify CSS variables in `css/styles.css` for color themes
- Adjust Neumorphism shadows and gradients
- Customize responsive breakpoints

### Functionality
- Add new expense categories in the forms
- Extend owner groups (OR2-OR5, GH14-GH15)
- Customize receipt templates

### Languages
- Add new language objects to `translations.js`
- Implement additional RTL languages
- Customize date/number formats

## 🐛 Troubleshooting

### Common Issues
1. **Data not saving**: Check browser localStorage permissions
2. **Language not switching**: Clear browser cache
3. **Receipts not printing**: Enable pop-ups in browser
4. **Mobile layout issues**: Update to latest browser version

### Browser Compatibility
- **Chrome**: 80+ ✅
- **Firefox**: 75+ ✅
- **Safari**: 13+ ✅
- **Edge**: 80+ ✅

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for:
- Bug fixes
- New features
- Translation improvements
- UI/UX enhancements

## 📞 Support

For support or questions:
- Create an issue in the project repository
- Check the troubleshooting section
- Review the code documentation

---

**Built with ❤️ for Syndic management efficiency**
