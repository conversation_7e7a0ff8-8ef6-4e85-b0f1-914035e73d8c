/**
 * Main Application Logic
 * Syndic Management System
 */

class SyndicApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.modals = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initializeModals();
        this.loadDashboard();
        this.hideLoadingScreen();
        this.checkNotifications();
    }
    
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }, 1000);
    }
    
    bindEvents() {
        // Navigation events
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.getAttribute('data-section');
                this.showSection(section);
            });
        });
        
        // Modal events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
            if (e.target.classList.contains('modal-close')) {
                this.closeModal();
            }
        });
        
        // Button events
        this.bindButtonEvents();
        
        // Search and filter events
        this.bindSearchEvents();
    }
    
    bindButtonEvents() {
        // Add owner button
        const addOwnerBtn = document.getElementById('add-owner-btn');
        if (addOwnerBtn) {
            addOwnerBtn.addEventListener('click', () => this.showAddOwnerModal());
        }
        
        // Add payment button
        const addPaymentBtn = document.getElementById('add-payment-btn');
        if (addPaymentBtn) {
            addPaymentBtn.addEventListener('click', () => this.showAddPaymentModal());
        }
        
        // Notifications button
        const notificationsBtn = document.getElementById('notifications-btn');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => this.showNotifications());
        }
    }
    
    bindSearchEvents() {
        // Owners search
        const ownersSearch = document.getElementById('owners-search');
        if (ownersSearch) {
            ownersSearch.addEventListener('input', (e) => {
                this.filterOwners(e.target.value);
            });
        }
        
        // Owners filter
        const ownersFilter = document.getElementById('owners-filter');
        if (ownersFilter) {
            ownersFilter.addEventListener('change', (e) => {
                this.filterOwnersByGroup(e.target.value);
            });
        }
        
        // Income search
        const incomeSearch = document.getElementById('income-search');
        if (incomeSearch) {
            incomeSearch.addEventListener('input', (e) => {
                this.filterPayments(e.target.value);
            });
        }
    }
    
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
        
        this.currentSection = sectionName;
        
        // Load section data
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'owners':
                this.loadOwners();
                break;
            case 'income':
                this.loadIncome();
                break;
            case 'expenses':
                this.loadExpenses();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'legal':
                this.loadLegal();
                break;
        }
    }
    
    loadDashboard() {
        const owners = window.storageManager.getOwners();
        const totalIncome = window.storageManager.getTotalIncome();
        const totalExpenses = window.storageManager.getTotalExpenses();
        const currentBalance = window.storageManager.getCurrentBalance();
        const overduePayments = window.storageManager.getOverduePayments();
        
        // Update statistics
        document.getElementById('total-owners').textContent = owners.length;
        document.getElementById('total-income').textContent = `${totalIncome.toLocaleString()} DH`;
        document.getElementById('total-expenses').textContent = `${totalExpenses.toLocaleString()} DH`;
        document.getElementById('current-balance').textContent = `${currentBalance.toLocaleString()} DH`;
        
        // Load recent payments
        this.loadRecentPayments();
        
        // Load overdue payments
        this.loadOverduePayments();
    }
    
    loadRecentPayments() {
        const payments = window.storageManager.getPayments()
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 5);
        
        const container = document.getElementById('recent-payments-list');
        if (container) {
            container.innerHTML = '';
            
            if (payments.length === 0) {
                container.innerHTML = '<p class="text-center text-secondary">Aucun paiement récent</p>';
                return;
            }
            
            payments.forEach(payment => {
                const owner = window.storageManager.getOwnerById(payment.ownerId);
                const paymentElement = document.createElement('div');
                paymentElement.className = 'recent-item';
                paymentElement.innerHTML = `
                    <div class="recent-item-content">
                        <strong>${owner ? owner.fullName : 'Propriétaire inconnu'}</strong>
                        <span>${payment.amount} DH - ${new Date(payment.date).toLocaleDateString()}</span>
                    </div>
                `;
                container.appendChild(paymentElement);
            });
        }
    }
    
    loadOverduePayments() {
        const overduePayments = window.storageManager.getOverduePayments().slice(0, 5);
        const container = document.getElementById('overdue-payments-list');
        
        if (container) {
            container.innerHTML = '';
            
            if (overduePayments.length === 0) {
                container.innerHTML = '<p class="text-center text-secondary">Aucun paiement en retard</p>';
                return;
            }
            
            overduePayments.forEach(overdue => {
                const overdueElement = document.createElement('div');
                overdueElement.className = 'recent-item overdue';
                overdueElement.innerHTML = `
                    <div class="recent-item-content">
                        <strong>${overdue.owner.fullName}</strong>
                        <span>Année ${overdue.year} - ${overdue.daysOverdue > 0 ? overdue.daysOverdue + ' jours' : 'En cours'}</span>
                    </div>
                `;
                container.appendChild(overdueElement);
            });
        }
    }
    
    loadOwners() {
        const owners = window.storageManager.getOwners();
        const tbody = document.getElementById('owners-tbody');
        
        if (tbody) {
            tbody.innerHTML = '';
            
            owners.forEach(owner => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${owner.fullName}</td>
                    <td>${owner.group}</td>
                    <td>${owner.building}</td>
                    <td>${owner.apartment}</td>
                    <td>${owner.titleNumber}</td>
                    <td>
                        <span class="status-badge ${owner.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${window.languageManager.getTranslation(owner.status)}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="app.editOwner('${owner.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteOwner('${owner.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    }
    
    loadIncome() {
        const payments = window.storageManager.getPayments();
        const tbody = document.getElementById('income-tbody');
        
        if (tbody) {
            tbody.innerHTML = '';
            
            payments.forEach(payment => {
                const owner = window.storageManager.getOwnerById(payment.ownerId);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${payment.receiptNumber}</td>
                    <td>${new Date(payment.date).toLocaleDateString()}</td>
                    <td>${owner ? owner.fullName : 'Propriétaire inconnu'}</td>
                    <td>${payment.amount} DH</td>
                    <td>${window.languageManager.getTranslation(payment.paymentMethod)}</td>
                    <td>${payment.year}</td>
                    <td>
                        <button class="btn btn-sm" onclick="app.printReceipt('${payment.id}')">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm" onclick="app.editPayment('${payment.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deletePayment('${payment.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // Populate year filter
        this.populateYearFilter();
    }
    
    populateYearFilter() {
        const yearFilter = document.getElementById('income-year-filter');
        if (yearFilter) {
            yearFilter.innerHTML = '<option value="" data-translate="all_years">Toutes les années</option>';
            
            for (let year = 2018; year <= 2032; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearFilter.appendChild(option);
            }
        }
    }
    
    loadExpenses() {
        // Implementation for expenses loading
        console.log('Loading expenses...');
    }
    
    loadReports() {
        // Implementation for reports loading
        console.log('Loading reports...');
    }
    
    loadLegal() {
        // Implementation for legal documents loading
        console.log('Loading legal documents...');
    }
    
    // Modal management
    initializeModals() {
        this.createOwnerModal();
        this.createPaymentModal();
    }
    
    createOwnerModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h2 data-translate="add_owner">Ajouter Propriétaire</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="owner-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="full_name">Nom Complet</label>
                                <input type="text" class="form-input" name="fullName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="group">Groupe</label>
                                <select class="form-select" name="group" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="OR2">OR2</option>
                                    <option value="OR3">OR3</option>
                                    <option value="OR4">OR4</option>
                                    <option value="OR5">OR5</option>
                                    <option value="GH14">GH14</option>
                                    <option value="GH15">GH15</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="building">Bâtiment</label>
                                <input type="text" class="form-input" name="building" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="apartment">Appartement</label>
                                <input type="text" class="form-input" name="apartment" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="title_number">N° Titre (T.F)</label>
                                <input type="text" class="form-input" name="titleNumber" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="phone">Téléphone</label>
                                <input type="tel" class="form-input" name="phone">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="email">Email</label>
                            <input type="email" class="form-input" name="email">
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" name="status" value="active" checked>
                            <label data-translate="active">Actif</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="app.closeModal()" data-translate="cancel">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="app.saveOwner()" data-translate="save">Enregistrer</button>
                </div>
            </div>
        `;
        
        this.modals.owner = modalHTML;
    }
    
    createPaymentModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h2 data-translate="add_payment">Enregistrer Paiement</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="payment-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="owner">Propriétaire</label>
                                <select class="form-select" name="ownerId" required id="payment-owner-select">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="amount">Montant (DH)</label>
                                <input type="number" class="form-input" name="amount" required min="0" step="0.01">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="date">Date</label>
                                <input type="date" class="form-input" name="date" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="year">Année</label>
                                <select class="form-select" name="year" required>
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="payment_method">Mode de Paiement</label>
                            <select class="form-select" name="paymentMethod" required>
                                <option value="">Sélectionner...</option>
                                <option value="cash" data-translate="cash">Espèces</option>
                                <option value="transfer" data-translate="transfer">Virement</option>
                                <option value="cheque" data-translate="cheque">Chèque</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="app.closeModal()" data-translate="cancel">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="app.savePayment()" data-translate="save">Enregistrer</button>
                </div>
            </div>
        `;
        
        this.modals.payment = modalHTML;
    }
    
    showModal(modalName) {
        const overlay = document.getElementById('modal-overlay');
        if (overlay && this.modals[modalName]) {
            overlay.innerHTML = this.modals[modalName];
            overlay.classList.add('active');
            
            // Translate modal content
            window.languageManager.translatePage();
            
            // Populate dropdowns if needed
            if (modalName === 'payment') {
                this.populatePaymentModal();
            }
        }
    }
    
    closeModal() {
        const overlay = document.getElementById('modal-overlay');
        if (overlay) {
            overlay.classList.remove('active');
            setTimeout(() => {
                overlay.innerHTML = '';
            }, 300);
        }
    }
    
    showAddOwnerModal() {
        this.showModal('owner');
    }
    
    showAddPaymentModal() {
        this.showModal('payment');
    }
    
    populatePaymentModal() {
        // Populate owners dropdown
        const ownerSelect = document.getElementById('payment-owner-select');
        if (ownerSelect) {
            const owners = window.storageManager.getOwners();
            ownerSelect.innerHTML = '<option value="">Sélectionner...</option>';
            
            owners.forEach(owner => {
                const option = document.createElement('option');
                option.value = owner.id;
                option.textContent = `${owner.fullName} - ${owner.group} ${owner.building}/${owner.apartment}`;
                ownerSelect.appendChild(option);
            });
        }
        
        // Populate years dropdown
        const yearSelect = document.querySelector('#payment-form select[name="year"]');
        if (yearSelect) {
            yearSelect.innerHTML = '<option value="">Sélectionner...</option>';
            
            for (let year = 2018; year <= 2032; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            }
        }
        
        // Set default date to today
        const dateInput = document.querySelector('#payment-form input[name="date"]');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
    }
    
    // CRUD Operations
    saveOwner() {
        const form = document.getElementById('owner-form');
        const formData = new FormData(form);
        
        const owner = {
            fullName: formData.get('fullName'),
            group: formData.get('group'),
            building: formData.get('building'),
            apartment: formData.get('apartment'),
            titleNumber: formData.get('titleNumber'),
            phone: formData.get('phone') || '',
            email: formData.get('email') || '',
            status: formData.get('status') ? 'active' : 'inactive'
        };
        
        const savedOwner = window.storageManager.addOwner(owner);
        if (savedOwner) {
            this.showNotification('success', 'Propriétaire ajouté avec succès');
            this.closeModal();
            if (this.currentSection === 'owners') {
                this.loadOwners();
            }
            this.loadDashboard();
        } else {
            this.showNotification('error', 'Erreur lors de l\'ajout du propriétaire');
        }
    }
    
    savePayment() {
        const form = document.getElementById('payment-form');
        const formData = new FormData(form);
        
        const payment = {
            ownerId: formData.get('ownerId'),
            amount: parseFloat(formData.get('amount')),
            date: formData.get('date'),
            year: parseInt(formData.get('year')),
            paymentMethod: formData.get('paymentMethod')
        };
        
        const savedPayment = window.storageManager.addPayment(payment);
        if (savedPayment) {
            this.showNotification('success', 'Paiement enregistré avec succès');
            this.closeModal();
            if (this.currentSection === 'income') {
                this.loadIncome();
            }
            this.loadDashboard();
        } else {
            this.showNotification('error', 'Erreur lors de l\'enregistrement du paiement');
        }
    }
    
    editOwner(ownerId) {
        // Implementation for editing owner
        console.log('Edit owner:', ownerId);
    }
    
    deleteOwner(ownerId) {
        if (confirm(window.languageManager.getTranslation('confirm_delete'))) {
            const success = window.storageManager.deleteOwner(ownerId);
            if (success) {
                this.showNotification('success', 'Propriétaire supprimé avec succès');
                this.loadOwners();
                this.loadDashboard();
            } else {
                this.showNotification('error', 'Erreur lors de la suppression');
            }
        }
    }
    
    editPayment(paymentId) {
        // Implementation for editing payment
        console.log('Edit payment:', paymentId);
    }
    
    deletePayment(paymentId) {
        if (confirm(window.languageManager.getTranslation('confirm_delete'))) {
            const success = window.storageManager.deletePayment(paymentId);
            if (success) {
                this.showNotification('success', 'Paiement supprimé avec succès');
                this.loadIncome();
                this.loadDashboard();
            } else {
                this.showNotification('error', 'Erreur lors de la suppression');
            }
        }
    }
    
    printReceipt(paymentId) {
        const payment = window.storageManager.getPaymentById(paymentId);
        if (payment) {
            window.reportsManager.printReceipt(payment);
        }
    }
    
    // Filtering and search
    filterOwners(searchTerm) {
        const rows = document.querySelectorAll('#owners-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }
    
    filterOwnersByGroup(group) {
        const rows = document.querySelectorAll('#owners-tbody tr');
        rows.forEach(row => {
            const groupCell = row.cells[1].textContent;
            row.style.display = !group || groupCell === group ? '' : 'none';
        });
    }
    
    filterPayments(searchTerm) {
        const rows = document.querySelectorAll('#income-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }
    
    // Notifications
    showNotification(type, message, title = '') {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        notification.innerHTML = `
            <i class="notification-icon ${iconMap[type]}"></i>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeNotification(notification);
        }, 5000);
        
        // Close button event
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.removeNotification(notification);
        });
    }
    
    removeNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    showNotifications() {
        // Implementation for showing notifications panel
        console.log('Show notifications panel');
    }
    
    checkNotifications() {
        const overduePayments = window.storageManager.getOverduePayments();
        const notificationCount = document.getElementById('notification-count');
        
        if (notificationCount) {
            notificationCount.textContent = overduePayments.length;
            notificationCount.style.display = overduePayments.length > 0 ? 'flex' : 'none';
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SyndicApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SyndicApp;
}
