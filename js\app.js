/**
 * Main Application Logic
 * Syndic Management System
 */

class SyndicApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.modals = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initializeModals();
        this.loadDashboard();
        this.hideLoadingScreen();
        this.checkNotifications();
    }
    
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }, 1000);
    }
    
    bindEvents() {
        // Navigation events
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.getAttribute('data-section');
                this.showSection(section);
            });
        });
        
        // Modal events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
            if (e.target.classList.contains('modal-close')) {
                this.closeModal();
            }
        });
        
        // Button events
        this.bindButtonEvents();
        
        // Search and filter events
        this.bindSearchEvents();
    }
    
    bindButtonEvents() {
        // Add owner button
        const addOwnerBtn = document.getElementById('add-owner-btn');
        if (addOwnerBtn) {
            addOwnerBtn.addEventListener('click', () => this.showAddOwnerModal());
        }
        
        // Add payment button
        const addPaymentBtn = document.getElementById('add-payment-btn');
        if (addPaymentBtn) {
            addPaymentBtn.addEventListener('click', () => this.showAddPaymentModal());
        }

        // Add expense button
        const addExpenseBtn = document.getElementById('add-expense-btn');
        if (addExpenseBtn) {
            addExpenseBtn.addEventListener('click', () => this.showAddExpenseModal());
        }

        // Add document button
        const addDocumentBtn = document.getElementById('add-document-btn');
        if (addDocumentBtn) {
            addDocumentBtn.addEventListener('click', () => this.showAddDocumentModal());
        }

        // Notifications button
        const notificationsBtn = document.getElementById('notifications-btn');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => this.showNotifications());
        }
    }
    
    bindSearchEvents() {
        // Owners search
        const ownersSearch = document.getElementById('owners-search');
        if (ownersSearch) {
            ownersSearch.addEventListener('input', (e) => {
                this.filterOwners(e.target.value);
            });
        }
        
        // Owners filter
        const ownersFilter = document.getElementById('owners-filter');
        if (ownersFilter) {
            ownersFilter.addEventListener('change', (e) => {
                this.filterOwnersByGroup(e.target.value);
            });
        }
        
        // Income search
        const incomeSearch = document.getElementById('income-search');
        if (incomeSearch) {
            incomeSearch.addEventListener('input', (e) => {
                this.filterPayments(e.target.value);
            });
        }

        // Expenses search
        const expensesSearch = document.getElementById('expenses-search');
        if (expensesSearch) {
            expensesSearch.addEventListener('input', (e) => {
                this.filterExpenses(e.target.value);
            });
        }

        // Documents search
        const documentsSearch = document.getElementById('documents-search');
        if (documentsSearch) {
            documentsSearch.addEventListener('input', (e) => {
                this.filterDocuments(e.target.value);
            });
        }
    }
    
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
        
        this.currentSection = sectionName;
        
        // Load section data
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'owners':
                this.loadOwners();
                break;
            case 'income':
                this.loadIncome();
                break;
            case 'expenses':
                this.loadExpenses();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'legal':
                this.loadLegal();
                break;
        }
    }
    
    loadDashboard() {
        const owners = window.storageManager.getOwners();
        const totalIncome = window.storageManager.getTotalIncome();
        const totalExpenses = window.storageManager.getTotalExpenses();
        const currentBalance = window.storageManager.getCurrentBalance();
        const overduePayments = window.storageManager.getOverduePayments();
        
        // Update statistics
        document.getElementById('total-owners').textContent = owners.length;
        document.getElementById('total-income').textContent = `${totalIncome.toLocaleString()} DH`;
        document.getElementById('total-expenses').textContent = `${totalExpenses.toLocaleString()} DH`;
        document.getElementById('current-balance').textContent = `${currentBalance.toLocaleString()} DH`;
        
        // Load recent payments
        this.loadRecentPayments();
        
        // Load overdue payments
        this.loadOverduePayments();
    }
    
    loadRecentPayments() {
        const payments = window.storageManager.getPayments()
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 5);
        
        const container = document.getElementById('recent-payments-list');
        if (container) {
            container.innerHTML = '';
            
            if (payments.length === 0) {
                container.innerHTML = '<p class="text-center text-secondary">Aucun paiement récent</p>';
                return;
            }
            
            payments.forEach(payment => {
                const owner = window.storageManager.getOwnerById(payment.ownerId);
                const paymentElement = document.createElement('div');
                paymentElement.className = 'recent-item';
                paymentElement.innerHTML = `
                    <div class="recent-item-content">
                        <strong>${owner ? owner.fullName : 'Propriétaire inconnu'}</strong>
                        <span>${payment.amount} DH - ${new Date(payment.date).toLocaleDateString()}</span>
                    </div>
                `;
                container.appendChild(paymentElement);
            });
        }
    }
    
    loadOverduePayments() {
        const overduePayments = window.storageManager.getOverduePayments().slice(0, 5);
        const container = document.getElementById('overdue-payments-list');
        
        if (container) {
            container.innerHTML = '';
            
            if (overduePayments.length === 0) {
                container.innerHTML = '<p class="text-center text-secondary">Aucun paiement en retard</p>';
                return;
            }
            
            overduePayments.forEach(overdue => {
                const overdueElement = document.createElement('div');
                overdueElement.className = 'recent-item overdue';
                overdueElement.innerHTML = `
                    <div class="recent-item-content">
                        <strong>${overdue.owner.fullName}</strong>
                        <span>Année ${overdue.year} - ${overdue.daysOverdue > 0 ? overdue.daysOverdue + ' jours' : 'En cours'}</span>
                    </div>
                `;
                container.appendChild(overdueElement);
            });
        }
    }
    
    loadOwners() {
        const owners = window.storageManager.getOwners();
        const tbody = document.getElementById('owners-tbody');
        
        if (tbody) {
            tbody.innerHTML = '';
            
            owners.forEach(owner => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${owner.fullName}</td>
                    <td>${owner.group}</td>
                    <td>${owner.building}</td>
                    <td>${owner.apartment}</td>
                    <td>${owner.titleNumber}</td>
                    <td>
                        <span class="status-badge ${owner.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${window.languageManager.getTranslation(owner.status)}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="app.editOwner('${owner.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteOwner('${owner.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    }
    
    loadIncome() {
        const payments = window.storageManager.getPayments();
        const tbody = document.getElementById('income-tbody');
        
        if (tbody) {
            tbody.innerHTML = '';
            
            payments.forEach(payment => {
                const owner = window.storageManager.getOwnerById(payment.ownerId);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${payment.receiptNumber}</td>
                    <td>${new Date(payment.date).toLocaleDateString()}</td>
                    <td>${owner ? owner.fullName : 'Propriétaire inconnu'}</td>
                    <td>${payment.amount} DH</td>
                    <td>${window.languageManager.getTranslation(payment.paymentMethod)}</td>
                    <td>${payment.year}</td>
                    <td>
                        <button class="btn btn-sm" onclick="app.printReceipt('${payment.id}')">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm" onclick="app.editPayment('${payment.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deletePayment('${payment.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // Populate year filter
        this.populateYearFilter();
    }
    
    populateYearFilter() {
        const yearFilter = document.getElementById('income-year-filter');
        if (yearFilter) {
            yearFilter.innerHTML = '<option value="" data-translate="all_years">Toutes les années</option>';
            
            for (let year = 2018; year <= 2032; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearFilter.appendChild(option);
            }
        }
    }
    
    loadExpenses() {
        const expenses = window.storageManager.getExpenses();
        const tbody = document.getElementById('expenses-tbody');

        if (tbody) {
            tbody.innerHTML = '';

            expenses.forEach(expense => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${new Date(expense.date).toLocaleDateString()}</td>
                    <td>${window.languageManager.getTranslation(expense.type)}</td>
                    <td>${expense.description}</td>
                    <td>${expense.amount} DH</td>
                    <td>
                        ${expense.invoiceFile ?
                            `<i class="fas fa-file-alt" title="Fichier joint"></i>` :
                            '<span class="text-secondary">-</span>'
                        }
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="app.editExpense('${expense.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteExpense('${expense.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    }

    loadReports() {
        // Reports section is already loaded with static content
        // The functionality is in the button click handlers
        console.log('Reports section loaded');
    }

    loadLegal() {
        const documents = window.storageManager.getDocuments();
        const tbody = document.getElementById('documents-tbody');

        if (tbody) {
            tbody.innerHTML = '';

            documents.forEach(document => {
                const owner = window.storageManager.getOwnerById(document.ownerId);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${window.languageManager.getTranslation(document.type)}</td>
                    <td>${owner ? owner.fullName : 'غير محدد'}</td>
                    <td>${new Date(document.date).toLocaleDateString()}</td>
                    <td>${document.description}</td>
                    <td>
                        ${document.documentFile ?
                            `<i class="fas fa-file-pdf" title="ملف مرفق"></i>` :
                            '<span class="text-secondary">-</span>'
                        }
                    </td>
                    <td>
                        <button class="btn btn-sm" onclick="app.editDocument('${document.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="app.deleteDocument('${document.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
    }
    
    // Modal management
    initializeModals() {
        this.createOwnerModal();
        this.createPaymentModal();
        this.createExpenseModal();
        this.createDocumentModal();
    }
    
    createOwnerModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h2 data-translate="add_owner">Ajouter Propriétaire</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="owner-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="full_name">Nom Complet</label>
                                <input type="text" class="form-input" name="fullName" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="group">Groupe</label>
                                <select class="form-select" name="group" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="OR2">OR2</option>
                                    <option value="OR3">OR3</option>
                                    <option value="OR4">OR4</option>
                                    <option value="OR5">OR5</option>
                                    <option value="GH14">GH14</option>
                                    <option value="GH15">GH15</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="building">Bâtiment</label>
                                <input type="text" class="form-input" name="building" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="apartment">Appartement</label>
                                <input type="text" class="form-input" name="apartment" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="title_number">N° Titre (T.F)</label>
                                <input type="text" class="form-input" name="titleNumber" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="phone">Téléphone</label>
                                <input type="tel" class="form-input" name="phone">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="email">Email</label>
                            <input type="email" class="form-input" name="email">
                        </div>
                        <div class="form-checkbox">
                            <input type="checkbox" name="status" value="active" checked>
                            <label data-translate="active">Actif</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="app.closeModal()" data-translate="cancel">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="app.saveOwner()" data-translate="save">Enregistrer</button>
                </div>
            </div>
        `;
        
        this.modals.owner = modalHTML;
    }
    
    createPaymentModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h2 data-translate="add_payment">Enregistrer Paiement</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="payment-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="owner">Propriétaire</label>
                                <select class="form-select" name="ownerId" required id="payment-owner-select">
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="amount">Montant (DH)</label>
                                <input type="number" class="form-input" name="amount" required min="0" step="0.01">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="date">Date</label>
                                <input type="date" class="form-input" name="date" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="year">Année</label>
                                <select class="form-select" name="year" required>
                                    <option value="">Sélectionner...</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="payment_method">Mode de Paiement</label>
                            <select class="form-select" name="paymentMethod" required>
                                <option value="">Sélectionner...</option>
                                <option value="cash" data-translate="cash">Espèces</option>
                                <option value="transfer" data-translate="transfer">Virement</option>
                                <option value="cheque" data-translate="cheque">Chèque</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="app.closeModal()" data-translate="cancel">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="app.savePayment()" data-translate="save">Enregistrer</button>
                </div>
            </div>
        `;
        
        this.modals.payment = modalHTML;
    }

    createExpenseModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h2 data-translate="add_expense">إضافة مصروف</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="expense-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="expense_type">نوع المصروف</label>
                                <select class="form-select" name="type" required>
                                    <option value="">اختيار...</option>
                                    <option value="maintenance" data-translate="maintenance">صيانة</option>
                                    <option value="electricity" data-translate="electricity">كهرباء</option>
                                    <option value="water" data-translate="water">ماء</option>
                                    <option value="security" data-translate="security">أمن</option>
                                    <option value="cleaning" data-translate="cleaning">تنظيف</option>
                                    <option value="other" data-translate="other">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="amount">المبلغ (DH)</label>
                                <input type="number" class="form-input" name="amount" required min="0" step="0.01">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="date">التاريخ</label>
                            <input type="date" class="form-input" name="date" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="description">الوصف</label>
                            <textarea class="form-textarea" name="description" required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="invoice_file">ملف الفاتورة (اختياري)</label>
                            <div class="file-upload">
                                <input type="file" name="invoiceFile" accept=".pdf,.jpg,.jpeg,.png">
                                <div class="file-upload-content">
                                    <i class="file-upload-icon fas fa-cloud-upload-alt"></i>
                                    <div class="file-upload-text" data-translate="attach_file">إرفاق ملف</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="app.closeModal()" data-translate="cancel">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="app.saveExpense()" data-translate="save">حفظ</button>
                </div>
            </div>
        `;

        this.modals.expense = modalHTML;
    }

    createDocumentModal() {
        const modalHTML = `
            <div class="modal">
                <div class="modal-header">
                    <h2 data-translate="add_document">إضافة وثيقة</h2>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="document-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label" data-translate="document_type">نوع الوثيقة</label>
                                <select class="form-select" name="type" required>
                                    <option value="">اختيار...</option>
                                    <option value="payment_notice" data-translate="payment_notice">إشعار دفع</option>
                                    <option value="ownership_certificate" data-translate="ownership_certificate">شهادة ملكية</option>
                                    <option value="court_order" data-translate="court_order">أمر محكمة</option>
                                    <option value="seizure_order" data-translate="seizure_order">أمر حجز</option>
                                    <option value="judgment_registration" data-translate="judgment_registration">تسجيل حكم</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" data-translate="owner">المالك</label>
                                <select class="form-select" name="ownerId" id="document-owner-select">
                                    <option value="">اختيار...</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="document_date">تاريخ الوثيقة</label>
                            <input type="date" class="form-input" name="date" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="description">الوصف</label>
                            <textarea class="form-textarea" name="description" required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label" data-translate="document_file">ملف الوثيقة (اختياري)</label>
                            <div class="file-upload">
                                <input type="file" name="documentFile" accept=".pdf,.doc,.docx">
                                <div class="file-upload-content">
                                    <i class="file-upload-icon fas fa-cloud-upload-alt"></i>
                                    <div class="file-upload-text" data-translate="attach_file">إرفاق ملف</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="app.closeModal()" data-translate="cancel">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="app.saveDocument()" data-translate="save">حفظ</button>
                </div>
            </div>
        `;

        this.modals.document = modalHTML;
    }
    
    showModal(modalName) {
        const overlay = document.getElementById('modal-overlay');
        if (overlay && this.modals[modalName]) {
            overlay.innerHTML = this.modals[modalName];
            overlay.classList.add('active');
            
            // Translate modal content
            window.languageManager.translatePage();
            
            // Populate dropdowns if needed
            if (modalName === 'payment') {
                this.populatePaymentModal();
            } else if (modalName === 'expense') {
                this.populateExpenseModal();
            } else if (modalName === 'document') {
                this.populateDocumentModal();
            }
        }
    }
    
    closeModal() {
        const overlay = document.getElementById('modal-overlay');
        if (overlay) {
            overlay.classList.remove('active');
            setTimeout(() => {
                overlay.innerHTML = '';
            }, 300);
        }
    }
    
    showAddOwnerModal() {
        this.showModal('owner');
    }
    
    showAddPaymentModal() {
        this.showModal('payment');
    }

    showAddExpenseModal() {
        this.showModal('expense');
    }

    showAddDocumentModal() {
        this.showModal('document');
    }
    
    populatePaymentModal() {
        // Populate owners dropdown
        const ownerSelect = document.getElementById('payment-owner-select');
        if (ownerSelect) {
            const owners = window.storageManager.getOwners();
            ownerSelect.innerHTML = '<option value="">Sélectionner...</option>';
            
            owners.forEach(owner => {
                const option = document.createElement('option');
                option.value = owner.id;
                option.textContent = `${owner.fullName} - ${owner.group} ${owner.building}/${owner.apartment}`;
                ownerSelect.appendChild(option);
            });
        }
        
        // Populate years dropdown
        const yearSelect = document.querySelector('#payment-form select[name="year"]');
        if (yearSelect) {
            yearSelect.innerHTML = '<option value="">Sélectionner...</option>';
            
            for (let year = 2018; year <= 2032; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            }
        }
        
        // Set default date to today
        const dateInput = document.querySelector('#payment-form input[name="date"]');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
    }

    populateExpenseModal() {
        // Set default date to today
        const dateInput = document.querySelector('#expense-form input[name="date"]');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
    }

    populateDocumentModal() {
        // Populate owners dropdown
        const ownerSelect = document.getElementById('document-owner-select');
        if (ownerSelect) {
            const owners = window.storageManager.getOwners();
            ownerSelect.innerHTML = '<option value="">اختيار...</option>';

            owners.forEach(owner => {
                const option = document.createElement('option');
                option.value = owner.id;
                option.textContent = `${owner.fullName} - ${owner.group} ${owner.building}/${owner.apartment}`;
                ownerSelect.appendChild(option);
            });
        }

        // Set default date to today
        const dateInput = document.querySelector('#document-form input[name="date"]');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
    }
    
    // CRUD Operations
    saveOwner() {
        const form = document.getElementById('owner-form');
        const formData = new FormData(form);
        
        const owner = {
            fullName: formData.get('fullName'),
            group: formData.get('group'),
            building: formData.get('building'),
            apartment: formData.get('apartment'),
            titleNumber: formData.get('titleNumber'),
            phone: formData.get('phone') || '',
            email: formData.get('email') || '',
            status: formData.get('status') ? 'active' : 'inactive'
        };
        
        const savedOwner = window.storageManager.addOwner(owner);
        if (savedOwner) {
            this.showNotification('success', 'Propriétaire ajouté avec succès');
            this.closeModal();
            if (this.currentSection === 'owners') {
                this.loadOwners();
            }
            this.loadDashboard();
        } else {
            this.showNotification('error', 'Erreur lors de l\'ajout du propriétaire');
        }
    }
    
    savePayment() {
        const form = document.getElementById('payment-form');
        const formData = new FormData(form);
        
        const payment = {
            ownerId: formData.get('ownerId'),
            amount: parseFloat(formData.get('amount')),
            date: formData.get('date'),
            year: parseInt(formData.get('year')),
            paymentMethod: formData.get('paymentMethod')
        };
        
        const savedPayment = window.storageManager.addPayment(payment);
        if (savedPayment) {
            this.showNotification('success', 'Paiement enregistré avec succès');
            this.closeModal();
            if (this.currentSection === 'income') {
                this.loadIncome();
            }
            this.loadDashboard();
        } else {
            this.showNotification('error', 'Erreur lors de l\'enregistrement du paiement');
        }
    }

    saveExpense() {
        const form = document.getElementById('expense-form');
        const formData = new FormData(form);

        const expense = {
            type: formData.get('type'),
            amount: parseFloat(formData.get('amount')),
            date: formData.get('date'),
            description: formData.get('description'),
            invoiceFile: formData.get('invoiceFile')?.name || null
        };

        const savedExpense = window.storageManager.addExpense(expense);
        if (savedExpense) {
            this.showNotification('success', 'مصروف مضاف بنجاح');
            this.closeModal();
            if (this.currentSection === 'expenses') {
                this.loadExpenses();
            }
            this.loadDashboard();
        } else {
            this.showNotification('error', 'خطأ في إضافة المصروف');
        }
    }

    saveDocument() {
        const form = document.getElementById('document-form');
        const formData = new FormData(form);

        const document = {
            type: formData.get('type'),
            ownerId: formData.get('ownerId'),
            date: formData.get('date'),
            description: formData.get('description'),
            documentFile: formData.get('documentFile')?.name || null
        };

        const savedDocument = window.storageManager.addDocument(document);
        if (savedDocument) {
            this.showNotification('success', 'وثيقة مضافة بنجاح');
            this.closeModal();
            if (this.currentSection === 'legal') {
                this.loadLegal();
            }
        } else {
            this.showNotification('error', 'خطأ في إضافة الوثيقة');
        }
    }
    
    editOwner(ownerId) {
        // Implementation for editing owner
        console.log('Edit owner:', ownerId);
    }
    
    deleteOwner(ownerId) {
        if (confirm(window.languageManager.getTranslation('confirm_delete'))) {
            const success = window.storageManager.deleteOwner(ownerId);
            if (success) {
                this.showNotification('success', 'Propriétaire supprimé avec succès');
                this.loadOwners();
                this.loadDashboard();
            } else {
                this.showNotification('error', 'Erreur lors de la suppression');
            }
        }
    }
    
    editPayment(paymentId) {
        // Implementation for editing payment
        console.log('Edit payment:', paymentId);
    }
    
    deletePayment(paymentId) {
        if (confirm(window.languageManager.getTranslation('confirm_delete'))) {
            const success = window.storageManager.deletePayment(paymentId);
            if (success) {
                this.showNotification('success', 'Paiement supprimé avec succès');
                this.loadIncome();
                this.loadDashboard();
            } else {
                this.showNotification('error', 'Erreur lors de la suppression');
            }
        }
    }
    
    printReceipt(paymentId) {
        const payment = window.storageManager.getPaymentById(paymentId);
        if (payment) {
            window.reportsManager.printReceipt(payment);
        }
    }

    // Expense management functions
    editExpense(expenseId) {
        // Implementation for editing expense
        console.log('Edit expense:', expenseId);
        this.showNotification('info', 'وظيفة التعديل قيد التطوير');
    }

    deleteExpense(expenseId) {
        if (confirm(window.languageManager.getTranslation('confirm_delete'))) {
            const success = window.storageManager.deleteExpense(expenseId);
            if (success) {
                this.showNotification('success', 'مصروف محذوف بنجاح');
                this.loadExpenses();
                this.loadDashboard();
            } else {
                this.showNotification('error', 'خطأ في الحذف');
            }
        }
    }

    // Document management functions
    editDocument(documentId) {
        // Implementation for editing document
        console.log('Edit document:', documentId);
        this.showNotification('info', 'وظيفة التعديل قيد التطوير');
    }

    deleteDocument(documentId) {
        if (confirm(window.languageManager.getTranslation('confirm_delete'))) {
            const success = window.storageManager.deleteDocument(documentId);
            if (success) {
                this.showNotification('success', 'وثيقة محذوفة بنجاح');
                this.loadLegal();
            } else {
                this.showNotification('error', 'خطأ في الحذف');
            }
        }
    }
    
    // Filtering and search
    filterOwners(searchTerm) {
        const rows = document.querySelectorAll('#owners-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }
    
    filterOwnersByGroup(group) {
        const rows = document.querySelectorAll('#owners-tbody tr');
        rows.forEach(row => {
            const groupCell = row.cells[1].textContent;
            row.style.display = !group || groupCell === group ? '' : 'none';
        });
    }
    
    filterPayments(searchTerm) {
        const rows = document.querySelectorAll('#income-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }

    filterExpenses(searchTerm) {
        const rows = document.querySelectorAll('#expenses-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }

    filterDocuments(searchTerm) {
        const rows = document.querySelectorAll('#documents-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }
    
    // Notifications
    showNotification(type, message, title = '') {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        notification.innerHTML = `
            <i class="notification-icon ${iconMap[type]}"></i>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeNotification(notification);
        }, 5000);
        
        // Close button event
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.removeNotification(notification);
        });
    }
    
    removeNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    showNotifications() {
        // Implementation for showing notifications panel
        console.log('Show notifications panel');
    }
    
    checkNotifications() {
        const overduePayments = window.storageManager.getOverduePayments();
        const notificationCount = document.getElementById('notification-count');

        if (notificationCount) {
            notificationCount.textContent = overduePayments.length;
            notificationCount.style.display = overduePayments.length > 0 ? 'flex' : 'none';
        }
    }

    // Report generation functions
    generateOwnerStatement() {
        this.showNotification('info', 'إنشاء كشف حساب المالك...');
        // Create a simple owner selection modal
        const owners = window.storageManager.getOwners();
        if (owners.length === 0) {
            this.showNotification('warning', 'لا يوجد ملاك مسجلين');
            return;
        }

        const ownerName = prompt('أدخل اسم المالك أو اختر من القائمة:\n' +
            owners.map((o, i) => `${i+1}. ${o.fullName}`).join('\n'));

        if (ownerName) {
            const selectedOwner = owners.find(o => o.fullName.includes(ownerName)) || owners[parseInt(ownerName) - 1];
            if (selectedOwner) {
                const statement = window.reportsManager.generateOwnerStatement(selectedOwner.id, 2018, 2024);
                this.showNotification('success', `تم إنشاء كشف حساب ${selectedOwner.fullName}`);
                console.log('Owner Statement:', statement);
            }
        }
    }

    generatePaymentReport() {
        this.showNotification('info', 'إنشاء تقرير المدفوعات...');
        const fromDate = prompt('من تاريخ (YYYY-MM-DD):', '2024-01-01');
        const toDate = prompt('إلى تاريخ (YYYY-MM-DD):', new Date().toISOString().split('T')[0]);

        if (fromDate && toDate) {
            const report = window.reportsManager.generatePaymentReport(fromDate, toDate);
            const data = report.payments.map(p => {
                const owner = window.storageManager.getOwnerById(p.ownerId);
                return {
                    'رقم الإيصال': p.receiptNumber,
                    'التاريخ': p.date,
                    'المالك': owner ? owner.fullName : 'غير معروف',
                    'المبلغ': p.amount,
                    'طريقة الدفع': p.paymentMethod,
                    'السنة': p.year
                };
            });

            window.reportsManager.exportToExcel(data, `تقرير_المدفوعات_${fromDate}_${toDate}`);
            this.showNotification('success', 'تم تصدير تقرير المدفوعات');
        }
    }

    generateExpenseReport() {
        this.showNotification('info', 'إنشاء تقرير المصروفات...');
        const fromDate = prompt('من تاريخ (YYYY-MM-DD):', '2024-01-01');
        const toDate = prompt('إلى تاريخ (YYYY-MM-DD):', new Date().toISOString().split('T')[0]);

        if (fromDate && toDate) {
            const report = window.reportsManager.generateExpenseReport(fromDate, toDate);
            const data = report.expenses.map(e => ({
                'التاريخ': e.date,
                'النوع': e.type,
                'الوصف': e.description,
                'المبلغ': e.amount
            }));

            window.reportsManager.exportToExcel(data, `تقرير_المصروفات_${fromDate}_${toDate}`);
            this.showNotification('success', 'تم تصدير تقرير المصروفات');
        }
    }

    generateBalanceReport() {
        this.showNotification('info', 'إنشاء تقرير الأرصدة...');
        const balanceReport = window.reportsManager.generateBalanceReport();

        const data = balanceReport.map(b => ({
            'السنة': b.year,
            'الإيرادات': b.income,
            'المصروفات': b.expenses,
            'الرصيد': b.balance
        }));

        window.reportsManager.exportToExcel(data, `تقرير_الأرصدة_${new Date().getFullYear()}`);
        this.showNotification('success', 'تم تصدير تقرير الأرصدة');
    }

    generateOverdueReport() {
        this.showNotification('info', 'إنشاء تقرير المتأخرات...');
        const overdueReport = window.reportsManager.generateOverdueReport();

        const data = overdueReport.map(o => ({
            'المالك': o.owner.fullName,
            'المجموعة': o.owner.group,
            'المبنى': o.owner.building,
            'الشقة': o.owner.apartment,
            'السنة المتأخرة': o.year,
            'أيام التأخير': o.daysOverdue
        }));

        window.reportsManager.exportToExcel(data, `تقرير_المتأخرات_${new Date().toISOString().split('T')[0]}`);
        this.showNotification('success', 'تم تصدير تقرير المتأخرات');
    }

    generateMaintenanceReport() {
        this.showNotification('info', 'إنشاء تقرير الصيانة...');
        const expenses = window.storageManager.getExpenses();
        const maintenanceExpenses = expenses.filter(e => e.type === 'maintenance');

        const data = maintenanceExpenses.map(e => ({
            'التاريخ': e.date,
            'الوصف': e.description,
            'المبلغ': e.amount
        }));

        window.reportsManager.exportToExcel(data, `تقرير_الصيانة_${new Date().getFullYear()}`);
        this.showNotification('success', 'تم تصدير تقرير الصيانة');
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SyndicApp();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SyndicApp;
}
