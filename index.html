<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syndic Management - Gestion Syndic</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="css/styles.css">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p data-translate="loading">Chargement...</p>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-building"></i>
                <span data-translate="app_title">Gestion Syndic</span>
            </div>
            
            <div class="nav-menu">
                <button class="nav-item" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span data-translate="dashboard">Tableau de bord</span>
                </button>
                <button class="nav-item" data-section="owners">
                    <i class="fas fa-users"></i>
                    <span data-translate="owners">Propriétaires</span>
                </button>
                <button class="nav-item" data-section="income">
                    <i class="fas fa-coins"></i>
                    <span data-translate="income">Recettes</span>
                </button>
                <button class="nav-item" data-section="expenses">
                    <i class="fas fa-receipt"></i>
                    <span data-translate="expenses">Dépenses</span>
                </button>
                <button class="nav-item" data-section="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span data-translate="reports">Rapports</span>
                </button>
                <button class="nav-item" data-section="legal">
                    <i class="fas fa-gavel"></i>
                    <span data-translate="legal">Documents</span>
                </button>
            </div>
            
            <div class="nav-controls">
                <button id="language-toggle" class="language-btn">
                    <i class="fas fa-language"></i>
                    <span id="current-lang">FR</span>
                </button>
                <button id="notifications-btn" class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span id="notification-count" class="notification-count">0</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="section-header">
                <h1 data-translate="dashboard">Tableau de bord</h1>
                <p data-translate="dashboard_subtitle">Vue d'ensemble de votre syndic</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-owners">0</h3>
                        <p data-translate="total_owners">Total Propriétaires</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-income">0 DH</h3>
                        <p data-translate="total_income">Recettes Totales</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-expenses">0 DH</h3>
                        <p data-translate="total_expenses">Dépenses Totales</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="current-balance">0 DH</h3>
                        <p data-translate="current_balance">Solde Actuel</p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3 data-translate="recent_payments">Paiements Récents</h3>
                    <div id="recent-payments-list" class="recent-list">
                        <!-- Recent payments will be populated here -->
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h3 data-translate="overdue_payments">Paiements en Retard</h3>
                    <div id="overdue-payments-list" class="recent-list">
                        <!-- Overdue payments will be populated here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Owners Section -->
        <section id="owners-section" class="content-section">
            <div class="section-header">
                <h1 data-translate="owners_management">Gestion des Propriétaires</h1>
                <button id="add-owner-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    <span data-translate="add_owner">Ajouter Propriétaire</span>
                </button>
            </div>
            
            <div class="table-container">
                <div class="table-controls">
                    <input type="text" id="owners-search" placeholder="Rechercher..." data-translate-placeholder="search_owners">
                    <select id="owners-filter">
                        <option value="" data-translate="all_groups">Tous les groupes</option>
                        <option value="OR2">OR2</option>
                        <option value="OR3">OR3</option>
                        <option value="OR4">OR4</option>
                        <option value="OR5">OR5</option>
                        <option value="GH14">GH14</option>
                        <option value="GH15">GH15</option>
                    </select>
                </div>
                
                <table id="owners-table" class="data-table">
                    <thead>
                        <tr>
                            <th data-translate="full_name">Nom Complet</th>
                            <th data-translate="group">Groupe</th>
                            <th data-translate="building">Bâtiment</th>
                            <th data-translate="apartment">Appartement</th>
                            <th data-translate="title_number">N° Titre</th>
                            <th data-translate="status">Statut</th>
                            <th data-translate="actions">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="owners-tbody">
                        <!-- Owners data will be populated here -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Income Section -->
        <section id="income-section" class="content-section">
            <div class="section-header">
                <h1 data-translate="income_management">Gestion des Recettes</h1>
                <button id="add-payment-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    <span data-translate="add_payment">Enregistrer Paiement</span>
                </button>
            </div>
            
            <div class="table-container">
                <div class="table-controls">
                    <input type="text" id="income-search" placeholder="Rechercher..." data-translate-placeholder="search_payments">
                    <input type="date" id="income-date-filter">
                    <select id="income-year-filter">
                        <option value="" data-translate="all_years">Toutes les années</option>
                    </select>
                </div>
                
                <table id="income-table" class="data-table">
                    <thead>
                        <tr>
                            <th data-translate="receipt_number">N° Reçu</th>
                            <th data-translate="date">Date</th>
                            <th data-translate="owner">Propriétaire</th>
                            <th data-translate="amount">Montant</th>
                            <th data-translate="payment_method">Mode Paiement</th>
                            <th data-translate="year">Année</th>
                            <th data-translate="actions">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="income-tbody">
                        <!-- Income data will be populated here -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Expenses Section -->
        <section id="expenses-section" class="content-section">
            <div class="section-header">
                <h1 data-translate="expenses_management">Gestion des Dépenses</h1>
                <button id="add-expense-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    <span data-translate="add_expense">Ajouter Dépense</span>
                </button>
            </div>

            <div class="table-container">
                <div class="table-controls">
                    <input type="text" id="expenses-search" placeholder="Rechercher..." data-translate-placeholder="search">
                    <input type="date" id="expenses-date-filter">
                    <select id="expenses-type-filter">
                        <option value="" data-translate="all">Tous les types</option>
                        <option value="maintenance" data-translate="maintenance">Maintenance</option>
                        <option value="electricity" data-translate="electricity">Électricité</option>
                        <option value="water" data-translate="water">Eau</option>
                        <option value="security" data-translate="security">Sécurité</option>
                        <option value="cleaning" data-translate="cleaning">Nettoyage</option>
                        <option value="other" data-translate="other">Autre</option>
                    </select>
                </div>

                <table id="expenses-table" class="data-table">
                    <thead>
                        <tr>
                            <th data-translate="date">Date</th>
                            <th data-translate="expense_type">Type</th>
                            <th data-translate="description">Description</th>
                            <th data-translate="amount">Montant</th>
                            <th data-translate="invoice_file">Facture</th>
                            <th data-translate="actions">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="expenses-tbody">
                        <!-- Expenses data will be populated here -->
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports-section" class="content-section">
            <div class="section-header">
                <h1 data-translate="reports_management">Gestion des Rapports</h1>
            </div>

            <div class="reports-grid">
                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="report-content">
                        <h3 data-translate="owner_statement">Relevé Propriétaire</h3>
                        <p data-translate="owner_statement_desc">Générer le relevé de compte d'un propriétaire</p>
                        <button class="btn btn-primary" onclick="app.generateOwnerStatement()">
                            <i class="fas fa-file-pdf"></i>
                            <span data-translate="generate_report">Générer</span>
                        </button>
                    </div>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="report-content">
                        <h3 data-translate="payment_report">Rapport Paiements</h3>
                        <p data-translate="payment_report_desc">Rapport détaillé des paiements</p>
                        <button class="btn btn-primary" onclick="app.generatePaymentReport()">
                            <i class="fas fa-file-excel"></i>
                            <span data-translate="generate_report">Générer</span>
                        </button>
                    </div>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="report-content">
                        <h3 data-translate="expense_report">Rapport Dépenses</h3>
                        <p data-translate="expense_report_desc">Analyse des dépenses par période</p>
                        <button class="btn btn-primary" onclick="app.generateExpenseReport()">
                            <i class="fas fa-file-pdf"></i>
                            <span data-translate="generate_report">Générer</span>
                        </button>
                    </div>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="report-content">
                        <h3 data-translate="balance_report">Rapport Soldes</h3>
                        <p data-translate="balance_report_desc">État des soldes par année</p>
                        <button class="btn btn-primary" onclick="app.generateBalanceReport()">
                            <i class="fas fa-file-excel"></i>
                            <span data-translate="generate_report">Générer</span>
                        </button>
                    </div>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="report-content">
                        <h3 data-translate="overdue_report">Rapport Retards</h3>
                        <p data-translate="overdue_report_desc">Liste des paiements en retard</p>
                        <button class="btn btn-warning" onclick="app.generateOverdueReport()">
                            <i class="fas fa-file-pdf"></i>
                            <span data-translate="generate_report">Générer</span>
                        </button>
                    </div>
                </div>

                <div class="report-card">
                    <div class="report-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="report-content">
                        <h3 data-translate="maintenance_report">Rapport Maintenance</h3>
                        <p data-translate="maintenance_report_desc">Récit des travaux effectués</p>
                        <button class="btn btn-success" onclick="app.generateMaintenanceReport()">
                            <i class="fas fa-file-pdf"></i>
                            <span data-translate="generate_report">Générer</span>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Legal Documents Section -->
        <section id="legal-section" class="content-section">
            <div class="section-header">
                <h1 data-translate="legal_management">Gestion Documents Légaux</h1>
                <button id="add-document-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    <span data-translate="add_document">Ajouter Document</span>
                </button>
            </div>

            <div class="table-container">
                <div class="table-controls">
                    <input type="text" id="documents-search" placeholder="Rechercher..." data-translate-placeholder="search">
                    <select id="documents-type-filter">
                        <option value="" data-translate="all">Tous les types</option>
                        <option value="payment_notice" data-translate="payment_notice">Avis de Paiement</option>
                        <option value="ownership_certificate" data-translate="ownership_certificate">Certificat de Propriété</option>
                        <option value="court_order" data-translate="court_order">Ordonnance Tribunal</option>
                        <option value="seizure_order" data-translate="seizure_order">Ordonnance Saisie</option>
                        <option value="judgment_registration" data-translate="judgment_registration">Enregistrement Jugement</option>
                    </select>
                </div>

                <table id="documents-table" class="data-table">
                    <thead>
                        <tr>
                            <th data-translate="document_type">Type</th>
                            <th data-translate="owner">Propriétaire</th>
                            <th data-translate="document_date">Date</th>
                            <th data-translate="description">Description</th>
                            <th data-translate="document_file">Fichier</th>
                            <th data-translate="actions">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="documents-tbody">
                        <!-- Documents data will be populated here -->
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <!-- Modals and overlays will be added here -->
    <div id="modal-overlay" class="modal-overlay"></div>
    
    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- JavaScript Files -->
    <script src="js/translations.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/demo-data.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
