<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصمم الإيصالات</title>
    <link rel="stylesheet" href="css/receipt-template.css">
    <style>
        /* إعدادات الصفحة */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        
        .designer-container {
            display: flex;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .controls-panel {
            width: 300px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .preview-panel {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .control-group input,
        .control-group select,
        .control-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .color-input {
            width: 50px !important;
            height: 40px;
            padding: 0;
            border: none;
            cursor: pointer;
        }
        
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #555;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #333;">🎨 مصمم الإيصالات</h1>
    
    <div class="designer-container">
        <!-- لوحة التحكم -->
        <div class="controls-panel">
            <h2>⚙️ إعدادات التصميم</h2>
            
            <div class="control-group">
                <label>اسم الشركة:</label>
                <input type="text" id="companyName" value="إدارة الأملاك المشتركة">
            </div>
            
            <div class="control-group">
                <label>عنوان الإيصال:</label>
                <input type="text" id="receiptTitle" value="إيصال استلام">
            </div>
            
            <div class="control-group">
                <label>رقم الإيصال:</label>
                <input type="text" id="receiptNumber" value="000001">
            </div>
            
            <h3>🎨 الألوان</h3>
            
            <div class="control-group">
                <label>لون الحدود:</label>
                <input type="color" id="borderColor" value="#000000" class="color-input">
            </div>
            
            <div class="control-group">
                <label>لون النص:</label>
                <input type="color" id="textColor" value="#000000" class="color-input">
            </div>
            
            <div class="control-group">
                <label>خلفية الرأس:</label>
                <input type="color" id="headerBg" value="#ffffff" class="color-input">
            </div>
            
            <div class="control-group">
                <label>خلفية المبلغ:</label>
                <input type="color" id="amountBg" value="#f9f9f9" class="color-input">
            </div>
            
            <h3>📝 الخط</h3>
            
            <div class="control-group">
                <label>نوع الخط:</label>
                <select id="fontFamily">
                    <option value="Arial, sans-serif">Arial</option>
                    <option value="'Times New Roman', serif">Times New Roman</option>
                    <option value="'Courier New', monospace">Courier New</option>
                    <option value="Tahoma, sans-serif">Tahoma</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>حجم الخط الأساسي:</label>
                <input type="range" id="fontSize" min="10" max="20" value="14">
                <span id="fontSizeValue">14px</span>
            </div>
            
            <h3>📏 المقاسات</h3>
            
            <div class="control-group">
                <label>عرض الإيصال:</label>
                <input type="range" id="receiptWidth" min="600" max="1000" value="800">
                <span id="widthValue">800px</span>
            </div>
            
            <div class="control-group">
                <label>سماكة الحدود:</label>
                <input type="range" id="borderWidth" min="1" max="5" value="2">
                <span id="borderWidthValue">2px</span>
            </div>
            
            <div style="margin-top: 30px;">
                <button class="btn btn-success" onclick="applyChanges()">✅ تطبيق التغييرات</button>
                <button class="btn" onclick="resetToDefault()">🔄 إعادة تعيين</button>
                <button class="btn" onclick="printReceipt()">🖨️ طباعة</button>
            </div>
        </div>
        
        <!-- لوحة المعاينة -->
        <div class="preview-panel">
            <h2>👁️ معاينة الإيصال</h2>
            
            <!-- الإيصال -->
            <div class="receipt-body">
                <div class="receipt-container" id="receiptContainer">
                    <div class="receipt-header" id="receiptHeader">
                        <div class="company-name" id="companyNamePreview">إدارة الأملاك المشتركة</div>
                        <div class="receipt-title" id="receiptTitlePreview">إيصال استلام</div>
                        <div class="receipt-number" id="receiptNumberPreview">رقم الإيصال: 000001</div>
                    </div>
                    
                    <div class="receipt-content">
                        <table class="info-table" id="infoTable">
                            <tr>
                                <td class="label">التاريخ</td>
                                <td class="value">01/01/2025</td>
                            </tr>
                            <tr>
                                <td class="label">استلم من</td>
                                <td class="value">أحمد محمد علي</td>
                            </tr>
                            <tr>
                                <td class="label">المجموعة</td>
                                <td class="value">GH15</td>
                            </tr>
                            <tr>
                                <td class="label">العمارة</td>
                                <td class="value">134</td>
                            </tr>
                            <tr>
                                <td class="label">الشقة</td>
                                <td class="value">11</td>
                            </tr>
                            <tr>
                                <td class="label">رقم السند</td>
                                <td class="value">45862/25</td>
                            </tr>
                            <tr>
                                <td class="label">عن سنة</td>
                                <td class="value">2025</td>
                            </tr>
                            <tr>
                                <td class="label">طريقة الدفع</td>
                                <td class="value">نقداً</td>
                            </tr>
                        </table>
                        
                        <div class="amount-section" id="amountSection">
                            <div class="amount-label">مبلغ وقدره:</div>
                            <div class="amount-value">1,000.00 درهم</div>
                            <div class="amount-words">ألف درهم</div>
                        </div>
                        
                        <div class="signature-section rtl">
                            <div class="signature-label">التوقيع</div>
                            <div class="signature-line"></div>
                        </div>
                    </div>
                    
                    <div class="receipt-footer" id="receiptFooter">
                        شكراً لكم على ثقتكم - إدارة الأملاك المشتركة
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تطبيق التغييرات
        function applyChanges() {
            const container = document.getElementById('receiptContainer');
            const header = document.getElementById('receiptHeader');
            const amountSection = document.getElementById('amountSection');
            const infoTable = document.getElementById('infoTable');
            const body = document.querySelector('.receipt-body');
            
            // النصوص
            document.getElementById('companyNamePreview').textContent = document.getElementById('companyName').value;
            document.getElementById('receiptTitlePreview').textContent = document.getElementById('receiptTitle').value;
            document.getElementById('receiptNumberPreview').textContent = 'رقم الإيصال: ' + document.getElementById('receiptNumber').value;
            
            // الألوان
            const borderColor = document.getElementById('borderColor').value;
            const textColor = document.getElementById('textColor').value;
            const headerBg = document.getElementById('headerBg').value;
            const amountBg = document.getElementById('amountBg').value;
            
            container.style.borderColor = borderColor;
            header.style.borderBottomColor = borderColor;
            header.style.background = headerBg;
            amountSection.style.borderColor = borderColor;
            amountSection.style.background = amountBg;
            body.style.color = textColor;
            
            // تطبيق لون الحدود على الجدول
            const cells = infoTable.querySelectorAll('td');
            cells.forEach(cell => {
                cell.style.borderColor = borderColor;
            });
            
            // الخط
            const fontFamily = document.getElementById('fontFamily').value;
            const fontSize = document.getElementById('fontSize').value + 'px';
            body.style.fontFamily = fontFamily;
            body.style.fontSize = fontSize;
            
            // المقاسات
            const width = document.getElementById('receiptWidth').value + 'px';
            const borderWidth = document.getElementById('borderWidth').value + 'px';
            container.style.maxWidth = width;
            container.style.borderWidth = borderWidth;
            header.style.borderBottomWidth = borderWidth;
            amountSection.style.borderWidth = borderWidth;
            
            // تحديث القيم المعروضة
            document.getElementById('fontSizeValue').textContent = fontSize;
            document.getElementById('widthValue').textContent = width;
            document.getElementById('borderWidthValue').textContent = borderWidth;
        }
        
        // إعادة تعيين للقيم الافتراضية
        function resetToDefault() {
            document.getElementById('companyName').value = 'إدارة الأملاك المشتركة';
            document.getElementById('receiptTitle').value = 'إيصال استلام';
            document.getElementById('receiptNumber').value = '000001';
            document.getElementById('borderColor').value = '#000000';
            document.getElementById('textColor').value = '#000000';
            document.getElementById('headerBg').value = '#ffffff';
            document.getElementById('amountBg').value = '#f9f9f9';
            document.getElementById('fontFamily').value = 'Arial, sans-serif';
            document.getElementById('fontSize').value = '14';
            document.getElementById('receiptWidth').value = '800';
            document.getElementById('borderWidth').value = '2';
            applyChanges();
        }
        
        // طباعة الإيصال
        function printReceipt() {
            const receiptContent = document.querySelector('.receipt-body').outerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة الإيصال</title>
                    <link rel="stylesheet" href="css/receipt-template.css">
                    <style>
                        body { margin: 0; background: white; }
                        .receipt-container { max-width: none; }
                    </style>
                </head>
                <body>
                    ${receiptContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
        
        // تطبيق التغييرات عند تحميل الصفحة
        window.onload = function() {
            applyChanges();
        };
    </script>
</body>
</html>
