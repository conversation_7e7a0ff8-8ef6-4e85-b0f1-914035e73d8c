/**
 * Reports and Export Management System
 * Handles PDF generation, Excel export, and various report types
 */

class ReportsManager {
    constructor() {
        this.init();
    }
    
    init() {
        // Load external libraries for PDF and Excel generation
        this.loadExternalLibraries();
    }
    
    loadExternalLibraries() {
        // Load jsPDF for PDF generation
        if (!window.jsPDF) {
            const script1 = document.createElement('script');
            script1.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            document.head.appendChild(script1);
        }
        
        // Load SheetJS for Excel generation
        if (!window.XLSX) {
            const script2 = document.createElement('script');
            script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            document.head.appendChild(script2);
        }
    }
    
    // Number to words conversion (French and Arabic)
    numberToWords(amount, language = 'fr') {
        const ones = {
            fr: ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf'],
            ar: ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة']
        };
        
        const tens = {
            fr: ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'],
            ar: ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون']
        };
        
        const teens = {
            fr: ['dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'],
            ar: ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر']
        };
        
        const hundreds = {
            fr: 'cent',
            ar: 'مائة'
        };
        
        const thousands = {
            fr: 'mille',
            ar: 'ألف'
        };
        
        if (amount === 0) return language === 'fr' ? 'zéro' : 'صفر';
        
        // Simple implementation for amounts up to 999,999
        let result = '';
        const num = Math.floor(amount);
        
        if (num >= 1000) {
            const thousandsDigit = Math.floor(num / 1000);
            if (thousandsDigit > 1) {
                result += ones[language][thousandsDigit] + ' ';
            }
            result += thousands[language] + ' ';
        }
        
        const remainder = num % 1000;
        if (remainder >= 100) {
            const hundredsDigit = Math.floor(remainder / 100);
            if (hundredsDigit > 1) {
                result += ones[language][hundredsDigit] + ' ';
            }
            result += hundreds[language] + ' ';
        }
        
        const lastTwo = remainder % 100;
        if (lastTwo >= 20) {
            const tensDigit = Math.floor(lastTwo / 10);
            const onesDigit = lastTwo % 10;
            result += tens[language][tensDigit];
            if (onesDigit > 0) {
                result += (language === 'fr' ? '-' : ' ') + ones[language][onesDigit];
            }
        } else if (lastTwo >= 10) {
            result += teens[language][lastTwo - 10];
        } else if (lastTwo > 0) {
            result += ones[language][lastTwo];
        }
        
        return result.trim();
    }
    
    // Generate payment receipt
    generateReceipt(payment) {
        const owner = window.storageManager.getOwnerById(payment.ownerId);
        const settings = window.storageManager.getSettings();
        const language = window.languageManager.getCurrentLanguage();
        
        const receiptData = {
            receiptNumber: payment.receiptNumber,
            date: new Date(payment.date).toLocaleDateString(language === 'ar' ? 'ar-MA' : 'fr-FR'),
            ownerName: owner.fullName,
            group: owner.group,
            building: owner.building,
            apartment: owner.apartment,
            titleNumber: owner.titleNumber,
            amount: payment.amount,
            amountWords: this.numberToWords(payment.amount, language),
            paymentMethod: payment.paymentMethod,
            year: payment.year,
            companyName: language === 'ar' ? 'إدارة الأملاك المشتركة' : (settings.companyName || 'Syndic de Copropriété')
        };
        
        return receiptData;
    }
    
    // Print receipt
    printReceipt(payment) {
        const receiptData = this.generateReceipt(payment);
        const language = window.languageManager.getCurrentLanguage();
        const isRTL = language === 'ar';
        
        const printWindow = window.open('', '_blank');
        const receiptHTML = `
            <!DOCTYPE html>
            <html lang="${language}" dir="${isRTL ? 'rtl' : 'ltr'}">
            <head>
                <meta charset="UTF-8">
                <title>${window.languageManager.getTranslation('receipt_title')}</title>
                <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
                <style>
                    body {
                        font-family: ${isRTL ? 'Noto Sans Arabic, Arial, sans-serif' : 'Inter, Arial, sans-serif'};
                        margin: 0;
                        padding: 20px;
                        direction: ${isRTL ? 'rtl' : 'ltr'};
                        background: #f5f5f5;
                    }
                    .receipt {
                        max-width: 700px;
                        margin: 0 auto;
                        background: white;
                        box-shadow: 0 0 20px rgba(0,0,0,0.1);
                        border-radius: 10px;
                        overflow: hidden;
                    }
                    .header {
                        background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
                        color: white;
                        padding: 30px;
                        text-align: center;
                        position: relative;
                    }
                    .logo {
                        width: 80px;
                        height: 80px;
                        background: white;
                        border-radius: 50%;
                        margin: 0 auto 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    }
                    .logo-icon {
                        font-size: 40px;
                        color: #2c5aa0;
                        font-weight: bold;
                    }
                    .company-name {
                        font-size: 28px;
                        font-weight: bold;
                        margin-bottom: 8px;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    }
                    .company-subtitle {
                        font-size: 16px;
                        opacity: 0.9;
                        margin-bottom: 20px;
                    }
                    .receipt-number {
                        background: rgba(255,255,255,0.2);
                        padding: 8px 20px;
                        border-radius: 20px;
                        font-size: 14px;
                        font-weight: 600;
                        display: inline-block;
                    }
                    .content {
                        padding: 40px;
                    }
                    .client-info {
                        background: #f8f9fa;
                        padding: 25px;
                        border-radius: 10px;
                        margin-bottom: 30px;
                        border-left: 4px solid #2c5aa0;
                    }
                    .info-row {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 12px;
                        padding: 8px 0;
                        border-bottom: 1px solid #e9ecef;
                    }
                    .info-row:last-child {
                        border-bottom: none;
                        margin-bottom: 0;
                    }
                    .label {
                        font-weight: 600;
                        color: #495057;
                        font-size: 14px;
                    }
                    .value {
                        font-weight: 500;
                        color: #212529;
                        font-size: 14px;
                    }
                    .amount-section {
                        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                        padding: 30px;
                        border-radius: 15px;
                        text-align: center;
                        margin: 30px 0;
                        border: 2px solid #2196f3;
                    }
                    .amount-label {
                        font-size: 16px;
                        color: #1976d2;
                        margin-bottom: 10px;
                        font-weight: 600;
                    }
                    .amount-value {
                        font-size: 32px;
                        font-weight: bold;
                        color: #0d47a1;
                        margin-bottom: 10px;
                    }
                    .amount-words {
                        font-size: 14px;
                        color: #1565c0;
                        font-style: italic;
                        background: white;
                        padding: 10px;
                        border-radius: 8px;
                        margin-top: 15px;
                    }
                    .payment-details {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 20px;
                        margin: 30px 0;
                    }
                    .detail-card {
                        background: white;
                        border: 1px solid #dee2e6;
                        border-radius: 8px;
                        padding: 20px;
                        text-align: center;
                    }
                    .detail-icon {
                        font-size: 24px;
                        color: #2c5aa0;
                        margin-bottom: 10px;
                    }
                    .detail-label {
                        font-size: 12px;
                        color: #6c757d;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        margin-bottom: 5px;
                    }
                    .detail-value {
                        font-size: 16px;
                        font-weight: 600;
                        color: #212529;
                    }
                    .footer {
                        background: #f8f9fa;
                        padding: 30px;
                        border-top: 1px solid #dee2e6;
                    }
                    .signature-section {
                        text-align: ${isRTL ? 'left' : 'right'};
                        margin-top: 40px;
                    }
                    .signature-label {
                        font-size: 14px;
                        color: #6c757d;
                        margin-bottom: 40px;
                    }
                    .signature-line {
                        border-bottom: 2px solid #2c5aa0;
                        width: 200px;
                        margin: ${isRTL ? '0 0 0 auto' : '0 auto 0 0'};
                    }
                    .company-footer {
                        text-align: center;
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #dee2e6;
                        font-size: 12px;
                        color: #6c757d;
                    }
                    @media print {
                        body {
                            margin: 0;
                            background: white;
                        }
                        .receipt {
                            box-shadow: none;
                            border-radius: 0;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <div class="logo">
                            <div class="logo-icon">🏢</div>
                        </div>
                        <div class="company-name">${receiptData.companyName}</div>
                        <div class="company-subtitle">${window.languageManager.getTranslation('receipt_subtitle')}</div>
                        <div class="receipt-number">${window.languageManager.getTranslation('receipt_number')}: ${receiptData.receiptNumber}</div>
                    </div>

                    <div class="content">
                        <div class="client-info">
                            <div class="info-row">
                                <span class="label">${window.languageManager.getTranslation('received_from')}:</span>
                                <span class="value">${receiptData.ownerName}</span>
                            </div>

                            <div class="info-row">
                                <span class="label">${window.languageManager.getTranslation('group')}:</span>
                                <span class="value">${receiptData.group}</span>
                            </div>

                            <div class="info-row">
                                <span class="label">${window.languageManager.getTranslation('building')}:</span>
                                <span class="value">${receiptData.building}</span>
                            </div>

                            <div class="info-row">
                                <span class="label">${window.languageManager.getTranslation('apartment')}:</span>
                                <span class="value">${receiptData.apartment}</span>
                            </div>

                            <div class="info-row">
                                <span class="label">${window.languageManager.getTranslation('title_number')}:</span>
                                <span class="value">${receiptData.titleNumber}</span>
                            </div>
                        </div>

                        <div class="amount-section">
                            <div class="amount-label">${window.languageManager.getTranslation('sum_of')}</div>
                            <div class="amount-value">${receiptData.amount.toLocaleString()} DH</div>
                            <div class="amount-words">${receiptData.amountWords} ${window.languageManager.getTranslation('dirhams')}</div>
                        </div>

                        <div class="payment-details">
                            <div class="detail-card">
                                <div class="detail-icon">📅</div>
                                <div class="detail-label">${window.languageManager.getTranslation('date')}</div>
                                <div class="detail-value">${receiptData.date}</div>
                            </div>

                            <div class="detail-card">
                                <div class="detail-icon">💳</div>
                                <div class="detail-label">${window.languageManager.getTranslation('payment_method')}</div>
                                <div class="detail-value">${window.languageManager.getTranslation(receiptData.paymentMethod)}</div>
                            </div>

                            <div class="detail-card">
                                <div class="detail-icon">📊</div>
                                <div class="detail-label">${window.languageManager.getTranslation('for_year')}</div>
                                <div class="detail-value">${receiptData.year}</div>
                            </div>

                            <div class="detail-card">
                                <div class="detail-icon">🏠</div>
                                <div class="detail-label">${window.languageManager.getTranslation('property')}</div>
                                <div class="detail-value">${receiptData.group} ${receiptData.building}/${receiptData.apartment}</div>
                            </div>
                        </div>
                    </div>

                    <div class="footer">
                        <div class="signature-section">
                            <div class="signature-label">${window.languageManager.getTranslation('signature')}</div>
                            <div class="signature-line"></div>
                        </div>

                        <div class="company-footer">
                            <strong>${receiptData.companyName}</strong><br>
                            ${window.languageManager.getTranslation('receipt_footer')}<br>
                            <small style="color: #999; margin-top: 10px; display: block;">
                                ${isRTL ? 'نظام إدارة الأملاك المشتركة' : 'Syndic Management System'} -
                                ${isRTL ? 'تم الإنشاء في' : 'Généré le'} ${new Date().toLocaleDateString(isRTL ? 'ar-MA' : 'fr-FR')}
                            </small>
                        </div>
                    </div>
                </div>
                
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `;
        
        printWindow.document.write(receiptHTML);
        printWindow.document.close();
    }
    
    // Generate owner statement report
    generateOwnerStatement(ownerId, fromYear, toYear) {
        const owner = window.storageManager.getOwnerById(ownerId);
        const payments = window.storageManager.getPaymentsByOwner(ownerId);
        
        const filteredPayments = payments.filter(payment => 
            payment.year >= fromYear && payment.year <= toYear
        );
        
        const totalPaid = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const yearsRange = [];
        for (let year = fromYear; year <= toYear; year++) {
            yearsRange.push(year);
        }
        
        const paidYears = filteredPayments.map(p => p.year);
        const unpaidYears = yearsRange.filter(year => !paidYears.includes(year));
        
        return {
            owner,
            payments: filteredPayments,
            totalPaid,
            unpaidYears,
            period: `${fromYear} - ${toYear}`
        };
    }
    
    // Export to Excel
    exportToExcel(data, filename) {
        if (!window.XLSX) {
            console.error('XLSX library not loaded');
            return;
        }
        
        const wb = window.XLSX.utils.book_new();
        
        if (Array.isArray(data)) {
            const ws = window.XLSX.utils.json_to_sheet(data);
            window.XLSX.utils.book_append_sheet(wb, ws, 'Data');
        } else {
            Object.keys(data).forEach(sheetName => {
                const ws = window.XLSX.utils.json_to_sheet(data[sheetName]);
                window.XLSX.utils.book_append_sheet(wb, ws, sheetName);
            });
        }
        
        window.XLSX.writeFile(wb, `${filename}.xlsx`);
    }
    
    // Export to PDF
    exportToPDF(content, filename) {
        if (!window.jsPDF) {
            console.error('jsPDF library not loaded');
            return;
        }
        
        const { jsPDF } = window.jsPDF;
        const doc = new jsPDF();
        
        // Add content to PDF
        doc.text(content, 10, 10);
        doc.save(`${filename}.pdf`);
    }
    
    // Generate various reports
    generatePaymentReport(fromDate, toDate) {
        const payments = window.storageManager.getPayments();
        const filteredPayments = payments.filter(payment => {
            const paymentDate = new Date(payment.date);
            return paymentDate >= new Date(fromDate) && paymentDate <= new Date(toDate);
        });
        
        return {
            payments: filteredPayments,
            totalAmount: filteredPayments.reduce((sum, p) => sum + p.amount, 0),
            count: filteredPayments.length,
            period: `${fromDate} - ${toDate}`
        };
    }
    
    generateExpenseReport(fromDate, toDate) {
        const expenses = window.storageManager.getExpenses();
        const filteredExpenses = expenses.filter(expense => {
            const expenseDate = new Date(expense.date);
            return expenseDate >= new Date(fromDate) && expenseDate <= new Date(toDate);
        });
        
        return {
            expenses: filteredExpenses,
            totalAmount: filteredExpenses.reduce((sum, e) => sum + e.amount, 0),
            count: filteredExpenses.length,
            period: `${fromDate} - ${toDate}`
        };
    }
    
    generateOverdueReport() {
        return window.storageManager.getOverduePayments();
    }
    
    generateBalanceReport() {
        const currentYear = new Date().getFullYear();
        const years = [];
        
        for (let year = 2018; year <= currentYear; year++) {
            years.push({
                year,
                income: window.storageManager.getTotalIncome(year),
                expenses: window.storageManager.getTotalExpenses(year),
                balance: window.storageManager.getCurrentBalance(year)
            });
        }
        
        return years;
    }
}

// Initialize reports manager
window.reportsManager = new ReportsManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReportsManager;
}
