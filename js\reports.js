/**
 * Reports and Export Management System
 * Handles PDF generation, Excel export, and various report types
 */

class ReportsManager {
    constructor() {
        this.init();
    }
    
    init() {
        // Load external libraries for PDF and Excel generation
        this.loadExternalLibraries();
    }
    
    loadExternalLibraries() {
        // Load jsPDF for PDF generation
        if (!window.jsPDF) {
            const script1 = document.createElement('script');
            script1.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            document.head.appendChild(script1);
        }
        
        // Load SheetJS for Excel generation
        if (!window.XLSX) {
            const script2 = document.createElement('script');
            script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            document.head.appendChild(script2);
        }
    }
    
    // Number to words conversion (French and Arabic)
    numberToWords(amount, language = 'fr') {
        const ones = {
            fr: ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf'],
            ar: ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة']
        };
        
        const tens = {
            fr: ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'],
            ar: ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون']
        };
        
        const teens = {
            fr: ['dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'],
            ar: ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر']
        };
        
        const hundreds = {
            fr: 'cent',
            ar: 'مائة'
        };
        
        const thousands = {
            fr: 'mille',
            ar: 'ألف'
        };
        
        if (amount === 0) return language === 'fr' ? 'zéro' : 'صفر';
        
        // Simple implementation for amounts up to 999,999
        let result = '';
        const num = Math.floor(amount);
        
        if (num >= 1000) {
            const thousandsDigit = Math.floor(num / 1000);
            if (thousandsDigit > 1) {
                result += ones[language][thousandsDigit] + ' ';
            }
            result += thousands[language] + ' ';
        }
        
        const remainder = num % 1000;
        if (remainder >= 100) {
            const hundredsDigit = Math.floor(remainder / 100);
            if (hundredsDigit > 1) {
                result += ones[language][hundredsDigit] + ' ';
            }
            result += hundreds[language] + ' ';
        }
        
        const lastTwo = remainder % 100;
        if (lastTwo >= 20) {
            const tensDigit = Math.floor(lastTwo / 10);
            const onesDigit = lastTwo % 10;
            result += tens[language][tensDigit];
            if (onesDigit > 0) {
                result += (language === 'fr' ? '-' : ' ') + ones[language][onesDigit];
            }
        } else if (lastTwo >= 10) {
            result += teens[language][lastTwo - 10];
        } else if (lastTwo > 0) {
            result += ones[language][lastTwo];
        }
        
        return result.trim();
    }
    
    // Generate payment receipt
    generateReceipt(payment) {
        const owner = window.storageManager.getOwnerById(payment.ownerId);
        const settings = window.storageManager.getSettings();
        const language = window.languageManager.getCurrentLanguage();
        
        const receiptData = {
            receiptNumber: payment.receiptNumber,
            date: new Date(payment.date).toLocaleDateString(language === 'ar' ? 'ar-MA' : 'fr-FR'),
            ownerName: owner.fullName,
            group: owner.group,
            building: owner.building,
            apartment: owner.apartment,
            titleNumber: owner.titleNumber,
            amount: payment.amount,
            amountWords: this.numberToWords(payment.amount, language),
            paymentMethod: payment.paymentMethod,
            year: payment.year,
            companyName: settings.companyName || 'Syndic de Copropriété'
        };
        
        return receiptData;
    }
    
    // Print receipt
    printReceipt(payment) {
        const receiptData = this.generateReceipt(payment);
        const language = window.languageManager.getCurrentLanguage();
        const isRTL = language === 'ar';
        
        const printWindow = window.open('', '_blank');
        const receiptHTML = `
            <!DOCTYPE html>
            <html lang="${language}" dir="${isRTL ? 'rtl' : 'ltr'}">
            <head>
                <meta charset="UTF-8">
                <title>${window.languageManager.getTranslation('receipt_title')}</title>
                <style>
                    body {
                        font-family: ${isRTL ? 'Arial, sans-serif' : 'Arial, sans-serif'};
                        margin: 20px;
                        direction: ${isRTL ? 'rtl' : 'ltr'};
                    }
                    .receipt {
                        max-width: 600px;
                        margin: 0 auto;
                        border: 2px solid #333;
                        padding: 20px;
                        background: white;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 1px solid #ccc;
                        padding-bottom: 20px;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }
                    .subtitle {
                        font-size: 16px;
                        color: #666;
                    }
                    .content {
                        line-height: 1.8;
                        font-size: 14px;
                    }
                    .row {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 10px;
                    }
                    .label {
                        font-weight: bold;
                    }
                    .amount {
                        font-size: 18px;
                        font-weight: bold;
                        text-align: center;
                        margin: 20px 0;
                        padding: 10px;
                        border: 1px solid #333;
                    }
                    .signature {
                        margin-top: 50px;
                        text-align: ${isRTL ? 'left' : 'right'};
                    }
                    @media print {
                        body { margin: 0; }
                        .receipt { border: none; }
                    }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <div class="title">${window.languageManager.getTranslation('receipt_title')}</div>
                        <div class="subtitle">${receiptData.companyName}</div>
                    </div>
                    
                    <div class="content">
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('receipt_number')}:</span>
                            <span>${receiptData.receiptNumber}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('date')}:</span>
                            <span>${receiptData.date}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('received_from')}:</span>
                            <span>${receiptData.ownerName}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('group')}:</span>
                            <span>${receiptData.group}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('building')}:</span>
                            <span>${receiptData.building}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('apartment')}:</span>
                            <span>${receiptData.apartment}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('title_number')}:</span>
                            <span>${receiptData.titleNumber}</span>
                        </div>
                        
                        <div class="amount">
                            <div>${window.languageManager.getTranslation('sum_of')}: ${receiptData.amount} ${window.languageManager.getTranslation('dirhams')}</div>
                            <div style="margin-top: 10px; font-size: 14px;">(${receiptData.amountWords} ${window.languageManager.getTranslation('dirhams')})</div>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('for_year')}:</span>
                            <span>${receiptData.year}</span>
                        </div>
                        
                        <div class="row">
                            <span class="label">${window.languageManager.getTranslation('payment_method')}:</span>
                            <span>${window.languageManager.getTranslation(receiptData.paymentMethod)}</span>
                        </div>
                        
                        <div class="signature">
                            <div>${window.languageManager.getTranslation('signature')}</div>
                            <div style="margin-top: 50px; border-bottom: 1px solid #333; width: 200px;"></div>
                        </div>
                    </div>
                </div>
                
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `;
        
        printWindow.document.write(receiptHTML);
        printWindow.document.close();
    }
    
    // Generate owner statement report
    generateOwnerStatement(ownerId, fromYear, toYear) {
        const owner = window.storageManager.getOwnerById(ownerId);
        const payments = window.storageManager.getPaymentsByOwner(ownerId);
        
        const filteredPayments = payments.filter(payment => 
            payment.year >= fromYear && payment.year <= toYear
        );
        
        const totalPaid = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const yearsRange = [];
        for (let year = fromYear; year <= toYear; year++) {
            yearsRange.push(year);
        }
        
        const paidYears = filteredPayments.map(p => p.year);
        const unpaidYears = yearsRange.filter(year => !paidYears.includes(year));
        
        return {
            owner,
            payments: filteredPayments,
            totalPaid,
            unpaidYears,
            period: `${fromYear} - ${toYear}`
        };
    }
    
    // Export to Excel
    exportToExcel(data, filename) {
        if (!window.XLSX) {
            console.error('XLSX library not loaded');
            return;
        }
        
        const wb = window.XLSX.utils.book_new();
        
        if (Array.isArray(data)) {
            const ws = window.XLSX.utils.json_to_sheet(data);
            window.XLSX.utils.book_append_sheet(wb, ws, 'Data');
        } else {
            Object.keys(data).forEach(sheetName => {
                const ws = window.XLSX.utils.json_to_sheet(data[sheetName]);
                window.XLSX.utils.book_append_sheet(wb, ws, sheetName);
            });
        }
        
        window.XLSX.writeFile(wb, `${filename}.xlsx`);
    }
    
    // Export to PDF
    exportToPDF(content, filename) {
        if (!window.jsPDF) {
            console.error('jsPDF library not loaded');
            return;
        }
        
        const { jsPDF } = window.jsPDF;
        const doc = new jsPDF();
        
        // Add content to PDF
        doc.text(content, 10, 10);
        doc.save(`${filename}.pdf`);
    }
    
    // Generate various reports
    generatePaymentReport(fromDate, toDate) {
        const payments = window.storageManager.getPayments();
        const filteredPayments = payments.filter(payment => {
            const paymentDate = new Date(payment.date);
            return paymentDate >= new Date(fromDate) && paymentDate <= new Date(toDate);
        });
        
        return {
            payments: filteredPayments,
            totalAmount: filteredPayments.reduce((sum, p) => sum + p.amount, 0),
            count: filteredPayments.length,
            period: `${fromDate} - ${toDate}`
        };
    }
    
    generateExpenseReport(fromDate, toDate) {
        const expenses = window.storageManager.getExpenses();
        const filteredExpenses = expenses.filter(expense => {
            const expenseDate = new Date(expense.date);
            return expenseDate >= new Date(fromDate) && expenseDate <= new Date(toDate);
        });
        
        return {
            expenses: filteredExpenses,
            totalAmount: filteredExpenses.reduce((sum, e) => sum + e.amount, 0),
            count: filteredExpenses.length,
            period: `${fromDate} - ${toDate}`
        };
    }
    
    generateOverdueReport() {
        return window.storageManager.getOverduePayments();
    }
    
    generateBalanceReport() {
        const currentYear = new Date().getFullYear();
        const years = [];
        
        for (let year = 2018; year <= currentYear; year++) {
            years.push({
                year,
                income: window.storageManager.getTotalIncome(year),
                expenses: window.storageManager.getTotalExpenses(year),
                balance: window.storageManager.getCurrentBalance(year)
            });
        }
        
        return years;
    }
}

// Initialize reports manager
window.reportsManager = new ReportsManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReportsManager;
}
