/**
 * Reports and Export Management System
 * Handles PDF generation, Excel export, and various report types
 */

class ReportsManager {
    constructor() {
        this.init();
    }
    
    init() {
        // Load external libraries for PDF and Excel generation
        this.loadExternalLibraries();
    }
    
    loadExternalLibraries() {
        // Load jsPDF for PDF generation
        if (!window.jsPDF) {
            const script1 = document.createElement('script');
            script1.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            document.head.appendChild(script1);
        }
        
        // Load SheetJS for Excel generation
        if (!window.XLSX) {
            const script2 = document.createElement('script');
            script2.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            document.head.appendChild(script2);
        }
    }
    
    // Number to words conversion (French and Arabic)
    numberToWords(amount, language = 'fr') {
        const ones = {
            fr: ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf'],
            ar: ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة']
        };
        
        const tens = {
            fr: ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'],
            ar: ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون']
        };
        
        const teens = {
            fr: ['dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'],
            ar: ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر']
        };
        
        const hundreds = {
            fr: 'cent',
            ar: 'مائة'
        };
        
        const thousands = {
            fr: 'mille',
            ar: 'ألف'
        };
        
        if (amount === 0) return language === 'fr' ? 'zéro' : 'صفر';
        
        // Simple implementation for amounts up to 999,999
        let result = '';
        const num = Math.floor(amount);
        
        if (num >= 1000) {
            const thousandsDigit = Math.floor(num / 1000);
            if (thousandsDigit > 1) {
                result += ones[language][thousandsDigit] + ' ';
            }
            result += thousands[language] + ' ';
        }
        
        const remainder = num % 1000;
        if (remainder >= 100) {
            const hundredsDigit = Math.floor(remainder / 100);
            if (hundredsDigit > 1) {
                result += ones[language][hundredsDigit] + ' ';
            }
            result += hundreds[language] + ' ';
        }
        
        const lastTwo = remainder % 100;
        if (lastTwo >= 20) {
            const tensDigit = Math.floor(lastTwo / 10);
            const onesDigit = lastTwo % 10;
            result += tens[language][tensDigit];
            if (onesDigit > 0) {
                result += (language === 'fr' ? '-' : ' ') + ones[language][onesDigit];
            }
        } else if (lastTwo >= 10) {
            result += teens[language][lastTwo - 10];
        } else if (lastTwo > 0) {
            result += ones[language][lastTwo];
        }
        
        return result.trim();
    }
    
    // Generate payment receipt
    generateReceipt(payment) {
        const owner = window.storageManager.getOwnerById(payment.ownerId);
        const settings = window.storageManager.getSettings();
        const language = window.languageManager.getCurrentLanguage();
        
        const receiptData = {
            receiptNumber: payment.receiptNumber,
            date: new Date(payment.date).toLocaleDateString(language === 'ar' ? 'ar-MA' : 'fr-FR'),
            ownerName: owner.fullName,
            group: owner.group,
            building: owner.building,
            apartment: owner.apartment,
            titleNumber: owner.titleNumber,
            amount: payment.amount,
            amountWords: this.numberToWords(payment.amount, language),
            paymentMethod: payment.paymentMethod,
            year: payment.year,
            companyName: language === 'ar' ? 'إدارة الأملاك المشتركة' : (settings.companyName || 'Syndic de Copropriété')
        };
        
        return receiptData;
    }
    
    // Print receipt
    printReceipt(payment) {
        const receiptData = this.generateReceipt(payment);
        const language = window.languageManager.getCurrentLanguage();
        const isRTL = language === 'ar';
        
        const printWindow = window.open('', '_blank');
        const receiptHTML = `
            <!DOCTYPE html>
            <html lang="${language}" dir="${isRTL ? 'rtl' : 'ltr'}">
            <head>
                <meta charset="UTF-8">
                <title>${window.languageManager.getTranslation('receipt_title')}</title>
                <link rel="stylesheet" href="css/receipt-template.css">
                <style>
                    /* تخصيصات خاصة بالطباعة */
                    body {
                        direction: ${isRTL ? 'rtl' : 'ltr'};
                    }

                    .signature-section {
                        text-align: ${isRTL ? 'left' : 'right'};
                    }

                    .signature-line {
                        margin: ${isRTL ? '0 0 0 auto' : '0 auto 0 0'};
                    }
                </style>
            </head>
            <body class="receipt-body">
                <div class="receipt-container">
                    <!-- رأس الإيصال -->
                    <div class="receipt-header">
                        <div class="company-name">${receiptData.companyName}</div>
                        <div class="receipt-title">${window.languageManager.getTranslation('receipt_title')}</div>
                        <div class="receipt-number">${window.languageManager.getTranslation('receipt_number')}: ${receiptData.receiptNumber}</div>
                    </div>

                    <!-- محتوى الإيصال -->
                    <div class="receipt-content">
                        <!-- جدول المعلومات -->
                        <table class="info-table">
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('date')}</td>
                                <td class="value">${receiptData.date}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('received_from')}</td>
                                <td class="value">${receiptData.ownerName}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('group')}</td>
                                <td class="value">${receiptData.group}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('building')}</td>
                                <td class="value">${receiptData.building}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('apartment')}</td>
                                <td class="value">${receiptData.apartment}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('title_number')}</td>
                                <td class="value">${receiptData.titleNumber}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('for_year')}</td>
                                <td class="value">${receiptData.year}</td>
                            </tr>
                            <tr>
                                <td class="label">${window.languageManager.getTranslation('payment_method')}</td>
                                <td class="value">${window.languageManager.getTranslation(receiptData.paymentMethod)}</td>
                            </tr>
                        </table>

                        <!-- قسم المبلغ -->
                        <div class="amount-section">
                            <div class="amount-label">${window.languageManager.getTranslation('sum_of')}:</div>
                            <div class="amount-value">${receiptData.amount.toLocaleString()} ${window.languageManager.getTranslation('dirhams')}</div>
                            <div class="amount-words">${receiptData.amountWords} ${window.languageManager.getTranslation('dirhams')}</div>
                        </div>

                        <!-- قسم التوقيع -->
                        <div class="signature-section">
                            <div class="signature-label">${window.languageManager.getTranslation('signature')}</div>
                            <div class="signature-line"></div>
                        </div>
                    </div>

                    <!-- أسفل الإيصال -->
                    <div class="receipt-footer">
                        ${window.languageManager.getTranslation('receipt_footer')} - ${receiptData.companyName}
                    </div>
                </div>
                
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `;
        
        printWindow.document.write(receiptHTML);
        printWindow.document.close();
    }
    
    // Generate owner statement report
    generateOwnerStatement(ownerId, fromYear, toYear) {
        const owner = window.storageManager.getOwnerById(ownerId);
        const payments = window.storageManager.getPaymentsByOwner(ownerId);
        
        const filteredPayments = payments.filter(payment => 
            payment.year >= fromYear && payment.year <= toYear
        );
        
        const totalPaid = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const yearsRange = [];
        for (let year = fromYear; year <= toYear; year++) {
            yearsRange.push(year);
        }
        
        const paidYears = filteredPayments.map(p => p.year);
        const unpaidYears = yearsRange.filter(year => !paidYears.includes(year));
        
        return {
            owner,
            payments: filteredPayments,
            totalPaid,
            unpaidYears,
            period: `${fromYear} - ${toYear}`
        };
    }
    
    // Export to Excel
    exportToExcel(data, filename) {
        if (!window.XLSX) {
            console.error('XLSX library not loaded');
            return;
        }
        
        const wb = window.XLSX.utils.book_new();
        
        if (Array.isArray(data)) {
            const ws = window.XLSX.utils.json_to_sheet(data);
            window.XLSX.utils.book_append_sheet(wb, ws, 'Data');
        } else {
            Object.keys(data).forEach(sheetName => {
                const ws = window.XLSX.utils.json_to_sheet(data[sheetName]);
                window.XLSX.utils.book_append_sheet(wb, ws, sheetName);
            });
        }
        
        window.XLSX.writeFile(wb, `${filename}.xlsx`);
    }
    
    // Export to PDF
    exportToPDF(content, filename) {
        if (!window.jsPDF) {
            console.error('jsPDF library not loaded');
            return;
        }
        
        const { jsPDF } = window.jsPDF;
        const doc = new jsPDF();
        
        // Add content to PDF
        doc.text(content, 10, 10);
        doc.save(`${filename}.pdf`);
    }
    
    // Generate various reports
    generatePaymentReport(fromDate, toDate) {
        const payments = window.storageManager.getPayments();
        const filteredPayments = payments.filter(payment => {
            const paymentDate = new Date(payment.date);
            return paymentDate >= new Date(fromDate) && paymentDate <= new Date(toDate);
        });
        
        return {
            payments: filteredPayments,
            totalAmount: filteredPayments.reduce((sum, p) => sum + p.amount, 0),
            count: filteredPayments.length,
            period: `${fromDate} - ${toDate}`
        };
    }
    
    generateExpenseReport(fromDate, toDate) {
        const expenses = window.storageManager.getExpenses();
        const filteredExpenses = expenses.filter(expense => {
            const expenseDate = new Date(expense.date);
            return expenseDate >= new Date(fromDate) && expenseDate <= new Date(toDate);
        });
        
        return {
            expenses: filteredExpenses,
            totalAmount: filteredExpenses.reduce((sum, e) => sum + e.amount, 0),
            count: filteredExpenses.length,
            period: `${fromDate} - ${toDate}`
        };
    }
    
    generateOverdueReport() {
        return window.storageManager.getOverduePayments();
    }
    
    generateBalanceReport() {
        const currentYear = new Date().getFullYear();
        const years = [];
        
        for (let year = 2018; year <= currentYear; year++) {
            years.push({
                year,
                income: window.storageManager.getTotalIncome(year),
                expenses: window.storageManager.getTotalExpenses(year),
                balance: window.storageManager.getCurrentBalance(year)
            });
        }
        
        return years;
    }
}

// Initialize reports manager
window.reportsManager = new ReportsManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReportsManager;
}
