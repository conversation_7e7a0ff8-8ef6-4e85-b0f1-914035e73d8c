# 🎨 دليل تصميم الإيصالات المخصصة

## 📋 الملفات المتاحة للتخصيص

### 1. **custom-receipt.html** - قالب فارغ للتصميم من الصفر
### 2. **css/receipt-template.css** - ملف الأنماط الأساسي
### 3. **js/reports.js** - ملف إنتاج الإيصالات

---

## 🛠️ كيفية تصميم إيصال مخصص

### الخطوة 1: افتح القالب الفارغ
```
http://localhost:8080/custom-receipt.html
```

### الخطوة 2: صمم الإيصال في قسم CSS
```css
/* مثال على تصميم مخصص */
.receipt {
    border: 3px solid #000;
    padding: 30px;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.company-info h2 {
    text-align: center;
    font-size: 28px;
    color: #333;
    border-bottom: 2px solid #000;
    padding-bottom: 10px;
}

.amount-info {
    background: #f0f0f0;
    padding: 20px;
    border: 2px solid #000;
    text-align: center;
    margin: 20px 0;
}
```

### الخطوة 3: عدل HTML حسب احتياجاتك
```html
<!-- أضف عناصر جديدة -->
<div class="company-logo">
    <img src="logo.png" alt="شعار الشركة">
</div>

<!-- أو غير ترتيب المعلومات -->
<table class="info-table">
    <tr>
        <td>البيان</td>
        <td>القيمة</td>
    </tr>
</table>
```

---

## 🎯 أفكار للتصميم

### 1. **تصميم كلاسيكي**
- حدود سوداء سميكة
- خط Times New Roman
- تخطيط جدولي منظم

### 2. **تصميم حديث**
- ألوان متدرجة
- ظلال ناعمة
- خطوط عصرية

### 3. **تصميم رسمي**
- شعار الشركة في الأعلى
- ألوان الشركة
- معلومات الاتصال

### 4. **تصميم مبسط**
- خلفية بيضاء
- نص أسود
- تخطيط نظيف

---

## 📐 عناصر الإيصال الأساسية

### ✅ **معلومات إجبارية:**
- اسم الشركة
- رقم الإيصال
- التاريخ
- اسم العميل
- المبلغ (بالأرقام والحروف)
- التوقيع

### 🔧 **معلومات اختيارية:**
- شعار الشركة
- عنوان الشركة
- رقم الهاتف
- البريد الإلكتروني
- رقم السجل التجاري

---

## 💡 نصائح للتصميم

### 1. **الوضوح أولاً**
- استخدم خطوط واضحة
- اجعل المبلغ بارز
- نظم المعلومات بشكل منطقي

### 2. **التوافق مع الطباعة**
- تجنب الألوان الفاتحة جداً
- استخدم حجم خط مناسب (12px+)
- اترك مساحات كافية

### 3. **الهوية البصرية**
- استخدم ألوان الشركة
- أضف الشعار
- حافظ على الطابع المهني

### 4. **سهولة القراءة**
- تباين جيد بين النص والخلفية
- تنظيم واضح للمعلومات
- استخدام العناوين والفواصل

---

## 🔄 تطبيق التصميم على النظام

### بعد الانتهاء من التصميم:

1. **انسخ CSS** من `custom-receipt.html`
2. **الصقه في** `css/receipt-template.css`
3. **انسخ HTML** من القالب
4. **عدل** `js/reports.js` ليستخدم التصميم الجديد
5. **اختبر** الإيصال من النظام

---

## 📱 أمثلة جاهزة

### تصميم 1: رسمي
```css
.receipt {
    border: 2px solid #000;
    font-family: 'Times New Roman', serif;
}
.company-info {
    text-align: center;
    border-bottom: 3px double #000;
}
```

### تصميم 2: عصري
```css
.receipt {
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border: 1px solid #ddd;
}
.amount-info {
    background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
}
```

### تصميم 3: مبسط
```css
.receipt {
    border: 1px solid #ccc;
    padding: 40px;
    line-height: 1.8;
}
.receipt * {
    border: none !important;
    background: transparent !important;
}
```

---

## 🚀 البدء السريع

1. افتح `custom-receipt.html`
2. عدل CSS في قسم `<style>`
3. عدل HTML في قسم `.receipt`
4. اختبر بالضغط على "طباعة"
5. احفظ التصميم بالضغط على "حفظ التصميم"

---

## 📞 المساعدة

إذا احتجت مساعدة في:
- تصميم عنصر معين
- إضافة ميزة جديدة
- حل مشكلة في التصميم
- تطبيق التصميم على النظام

فقط اطلب المساعدة وسأساعدك! 🤝
