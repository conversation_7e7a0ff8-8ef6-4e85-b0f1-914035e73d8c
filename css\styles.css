/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Neumorphism Color Palette */
    --primary-bg: #e6e7ee;
    --secondary-bg: #f0f0f3;
    --card-bg: #e6e7ee;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    
    /* Neumorphism Shadows */
    --shadow-light: #ffffff;
    --shadow-dark: #d1d9e6;
    --shadow-inset-light: inset 2px 2px 5px #d1d9e6;
    --shadow-inset-dark: inset -2px -2px 5px #ffffff;
    --shadow-outset: 9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff;
    --shadow-pressed: inset 9px 9px 16px #d1d9e6, inset -9px -9px 16px #ffffff;
    
    /* Typography */
    --font-primary: 'Inter', sans-serif;
    --font-arabic: 'Noto Sans Arabic', sans-serif;
    
    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* RTL Support */
[dir="rtl"] {
    font-family: var(--font-arabic);
}

body {
    font-family: var(--font-primary);
    background: var(--primary-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-bg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-bg);
    box-shadow: var(--shadow-outset);
    position: relative;
    animation: spin 2s linear infinite;
}

.loading-spinner::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    background: var(--accent-color);
    opacity: 0.3;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== NAVIGATION ===== */
.navbar {
    background: var(--secondary-bg);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0 2rem;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--accent-color);
}

.nav-menu {
    display: flex;
    gap: 1rem;
}

.nav-item {
    background: var(--card-bg);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 5px 5px 10px var(--shadow-dark), -5px -5px 10px var(--shadow-light);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-item:hover {
    transform: translateY(-2px);
    box-shadow: 7px 7px 14px var(--shadow-dark), -7px -7px 14px var(--shadow-light);
}

.nav-item.active {
    box-shadow: var(--shadow-pressed);
    color: var(--accent-color);
}

.nav-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.language-btn, .notification-btn {
    background: var(--card-bg);
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 5px 5px 10px var(--shadow-dark), -5px -5px 10px var(--shadow-light);
    color: var(--text-primary);
    position: relative;
}

.language-btn:hover, .notification-btn:hover {
    transform: translateY(-2px);
    box-shadow: 7px 7px 14px var(--shadow-dark), -7px -7px 14px var(--shadow-light);
}

.notification-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

/* ===== MAIN CONTENT ===== */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 70px);
}

.content-section {
    display: none;
    animation: fadeIn 0.5s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-header p {
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* ===== BUTTONS ===== */
.btn {
    background: var(--card-bg);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 5px 5px 10px var(--shadow-dark), -5px -5px 10px var(--shadow-light);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 7px 7px 14px var(--shadow-dark), -7px -7px 14px var(--shadow-light);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-pressed);
}

.btn-primary {
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
    box-shadow: 5px 5px 10px rgba(52, 152, 219, 0.3), -5px -5px 10px rgba(52, 152, 219, 0.1);
}

.btn-success {
    background: linear-gradient(145deg, #27ae60, #229954);
    color: white;
}

.btn-warning {
    background: linear-gradient(145deg, #f39c12, #e67e22);
    color: white;
}

.btn-danger {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    color: white;
}

/* ===== CARDS ===== */
.stat-card, .dashboard-card {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: var(--shadow-outset);
    transition: var(--transition);
}

.stat-card:hover, .dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 12px 12px 20px var(--shadow-dark), -12px -12px 20px var(--shadow-light);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--card-bg);
    box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--accent-color);
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

/* ===== TABLES ===== */
.table-container {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: var(--shadow-outset);
    overflow: hidden;
}

.table-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.table-controls input,
.table-controls select {
    background: var(--secondary-bg);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition);
    min-width: 200px;
}

.table-controls input:focus,
.table-controls select:focus {
    outline: none;
    box-shadow: inset 3px 3px 6px var(--shadow-dark), inset -3px -3px 6px var(--shadow-light), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.data-table th {
    background: var(--secondary-bg);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
}

.data-table tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .navbar {
        padding: 0 1rem;
    }
    
    .nav-menu {
        display: none;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .table-controls {
        flex-direction: column;
    }
    
    .table-controls input,
    .table-controls select {
        min-width: 100%;
    }
}

/* ===== UTILITY CLASSES ===== */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

/* ===== MODALS ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-outset);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.modal-header h2 {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-close {
    background: var(--card-bg);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 3px 3px 6px var(--shadow-dark), -3px -3px 6px var(--shadow-light);
    color: var(--text-secondary);
}

.modal-close:hover {
    color: var(--danger-color);
    transform: rotate(90deg);
}

.modal-body {
    margin-bottom: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    background: var(--secondary-bg);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    box-shadow: inset 3px 3px 6px var(--shadow-dark), inset -3px -3px 6px var(--shadow-light), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.form-checkbox input[type="checkbox"] {
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--secondary-bg);
    border-radius: 4px;
    box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
}

.form-checkbox input[type="checkbox"]:checked {
    background: var(--accent-color);
    box-shadow: inset 2px 2px 4px rgba(0,0,0,0.2);
}

.form-checkbox input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* ===== NOTIFICATIONS ===== */
.notification-container {
    position: fixed;
    top: 90px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.notification {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-outset);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    transform: translateX(100%);
    transition: var(--transition);
    border-left: 4px solid var(--accent-color);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification-icon {
    font-size: 1.2rem;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.notification-message {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition);
}

.notification-close:hover {
    background: rgba(0,0,0,0.1);
    color: var(--text-primary);
}

/* ===== REPORTS GRID ===== */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.report-card {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-outset);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 12px 12px 20px var(--shadow-dark), -12px -12px 20px var(--shadow-light);
}

.report-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--card-bg);
    box-shadow: var(--shadow-inset-light), var(--shadow-inset-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
}

.report-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.report-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

/* ===== STATUS BADGES ===== */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: linear-gradient(145deg, #d4edda, #c3e6cb);
    color: var(--success-color);
    box-shadow: 2px 2px 4px rgba(39, 174, 96, 0.2);
}

.status-inactive {
    background: linear-gradient(145deg, #f8d7da, #f5c6cb);
    color: var(--danger-color);
    box-shadow: 2px 2px 4px rgba(231, 76, 60, 0.2);
}

/* ===== RECENT ITEMS ===== */
.recent-list {
    max-height: 300px;
    overflow-y: auto;
}

.recent-item {
    padding: 1rem;
    border-radius: 12px;
    background: var(--secondary-bg);
    margin-bottom: 0.75rem;
    box-shadow: inset 2px 2px 4px var(--shadow-dark), inset -2px -2px 4px var(--shadow-light);
    transition: var(--transition);
}

.recent-item:hover {
    background: rgba(52, 152, 219, 0.05);
}

.recent-item.overdue {
    border-left: 4px solid var(--warning-color);
}

.recent-item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.recent-item-content strong {
    color: var(--text-primary);
    font-weight: 600;
}

.recent-item-content span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== BUTTON VARIANTS ===== */
.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 8px;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 16px;
}

/* ===== FILE UPLOAD ===== */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-outset);
    transition: var(--transition);
    text-align: center;
    border: 2px dashed var(--text-secondary);
}

.file-upload:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.file-upload-icon {
    font-size: 2rem;
    color: var(--accent-color);
}

.file-upload-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== LOADING STATES ===== */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .modal-overlay,
    .notification-container,
    .btn,
    .table-controls {
        display: none !important;
    }

    .main-content {
        padding: 0;
        max-width: none;
    }

    .content-section {
        display: block !important;
    }

    .table-container {
        box-shadow: none;
        border: 1px solid #000;
    }

    .data-table th,
    .data-table td {
        border: 1px solid #000;
        padding: 0.5rem;
    }
}

/* ===== RTL SPECIFIC STYLES ===== */
[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .section-header {
    text-align: right;
}

[dir="rtl"] .table-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-footer {
    flex-direction: row-reverse;
}

[dir="rtl"] .notification {
    border-right: 4px solid var(--accent-color);
    border-left: none;
}

[dir="rtl"] .recent-item.overdue {
    border-right: 4px solid var(--warning-color);
    border-left: none;
}
