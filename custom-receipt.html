<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم الإيصال المخصص</title>
    <style>
        /* ===== ضع تصميمك هنا ===== */
        
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: white;
            direction: rtl;
        }
        
        .receipt {
            /* صمم الإيصال كما تريد */
            width: 800px;
            margin: 0 auto;
            background: white;
            /* أضف تصميمك هنا */
        }
        
        /* ===== أضف أنماطك المخصصة هنا ===== */
        
    </style>
</head>
<body>
    <div class="receipt">
        <!-- ===== صمم الإيصال هنا ===== -->
        
        <h1>إيصال استلام</h1>
        
        <!-- معلومات الشركة -->
        <div class="company-info">
            <h2 id="companyName">اسم الشركة</h2>
        </div>
        
        <!-- رقم الإيصال والتاريخ -->
        <div class="receipt-info">
            <p>رقم الإيصال: <span id="receiptNumber">000001</span></p>
            <p>التاريخ: <span id="receiptDate">01/01/2025</span></p>
        </div>
        
        <!-- معلومات العميل -->
        <div class="client-info">
            <p>استلم من: <span id="clientName">اسم العميل</span></p>
            <p>المجموعة: <span id="group">GH15</span></p>
            <p>العمارة: <span id="building">134</span></p>
            <p>الشقة: <span id="apartment">11</span></p>
            <p>رقم السند: <span id="titleNumber">45862/25</span></p>
        </div>
        
        <!-- المبلغ -->
        <div class="amount-info">
            <p>مبلغ وقدره: <span id="amount">1000.00</span> درهم</p>
            <p>بالحروف: <span id="amountWords">ألف درهم</span></p>
        </div>
        
        <!-- تفاصيل إضافية -->
        <div class="additional-info">
            <p>عن سنة: <span id="year">2025</span></p>
            <p>طريقة الدفع: <span id="paymentMethod">نقداً</span></p>
        </div>
        
        <!-- التوقيع -->
        <div class="signature">
            <p>التوقيع: ________________</p>
        </div>
        
        <!-- ===== انتهى التصميم الأساسي ===== -->
    </div>

    <!-- أدوات التحكم -->
    <div style="margin-top: 50px; padding: 20px; background: #f0f0f0; border-radius: 10px;">
        <h3>🛠️ أدوات التحكم</h3>
        
        <div style="margin-bottom: 15px;">
            <label>اسم الشركة:</label>
            <input type="text" id="companyInput" value="إدارة الأملاك المشتركة" onchange="updateField('companyName', this.value)">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>رقم الإيصال:</label>
            <input type="text" id="receiptInput" value="000001" onchange="updateField('receiptNumber', this.value)">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>اسم العميل:</label>
            <input type="text" id="clientInput" value="أحمد محمد علي" onchange="updateField('clientName', this.value)">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>المبلغ:</label>
            <input type="number" id="amountInput" value="1000" onchange="updateAmount(this.value)">
        </div>
        
        <button onclick="printReceipt()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">🖨️ طباعة</button>
        
        <button onclick="saveDesign()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">💾 حفظ التصميم</button>
    </div>

    <script>
        // تحديث الحقول
        function updateField(fieldId, value) {
            document.getElementById(fieldId).textContent = value;
        }
        
        // تحديث المبلغ
        function updateAmount(amount) {
            document.getElementById('amount').textContent = parseFloat(amount).toFixed(2);
            // يمكنك إضافة تحويل المبلغ إلى كلمات هنا
            document.getElementById('amountWords').textContent = convertToWords(amount) + ' درهم';
        }
        
        // تحويل الأرقام إلى كلمات (مبسط)
        function convertToWords(num) {
            const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
            const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
            const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
            
            if (num == 1000) return 'ألف';
            if (num == 2000) return 'ألفان';
            if (num < 1000) {
                // تحويل مبسط للأرقام أقل من 1000
                return 'مبلغ ' + num;
            }
            return 'مبلغ ' + num;
        }
        
        // طباعة الإيصال
        function printReceipt() {
            const receiptContent = document.querySelector('.receipt').outerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة الإيصال</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        .receipt { width: 800px; margin: 0 auto; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${receiptContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
        
        // حفظ التصميم
        function saveDesign() {
            const css = document.querySelector('style').innerHTML;
            const html = document.querySelector('.receipt').outerHTML;
            
            const designData = {
                css: css,
                html: html,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem('customReceiptDesign', JSON.stringify(designData));
            alert('تم حفظ التصميم بنجاح! ✅');
        }
        
        // تحميل التصميم المحفوظ
        function loadSavedDesign() {
            const saved = localStorage.getItem('customReceiptDesign');
            if (saved) {
                const designData = JSON.parse(saved);
                // يمكنك تطبيق التصميم المحفوظ هنا
                console.log('تصميم محفوظ موجود:', designData);
            }
        }
        
        // تحميل التصميم عند فتح الصفحة
        window.onload = function() {
            loadSavedDesign();
        };
    </script>
</body>
</html>
