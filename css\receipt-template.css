/* ===== قالب تصميم الإيصال - قابل للتخصيص بسهولة ===== */

/* ===== الإعدادات العامة ===== */
.receipt-body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: white;
    color: #000;
    line-height: 1.6;
    font-size: 14px;
}

.receipt-container {
    max-width: 800px;
    margin: 0 auto;
    border: 2px solid #000;
    padding: 0;
    background: white;
}

/* ===== رأس الإيصال ===== */
.receipt-header {
    text-align: center;
    padding: 20px;
    border-bottom: 2px solid #000;
    background: white;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    text-transform: uppercase;
    color: #000;
}

.receipt-title {
    font-size: 20px;
    font-weight: bold;
    margin: 15px 0;
    text-decoration: underline;
    color: #000;
}

.receipt-number {
    font-size: 16px;
    margin: 10px 0;
    font-weight: bold;
    color: #000;
}

/* ===== محتوى الإيصال ===== */
.receipt-content {
    padding: 30px;
}

.info-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
}

.info-table td {
    padding: 12px;
    border: 1px solid #000;
    font-size: 14px;
}

.info-table .label {
    background: #f0f0f0;
    font-weight: bold;
    width: 30%;
    color: #000;
}

.info-table .value {
    background: white;
    width: 70%;
    color: #000;
}

/* ===== قسم المبلغ ===== */
.amount-section {
    border: 3px solid #000;
    padding: 20px;
    margin: 30px 0;
    text-align: center;
    background: #f9f9f9;
}

.amount-label {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #000;
}

.amount-value {
    font-size: 28px;
    font-weight: bold;
    margin: 15px 0;
    color: #000;
}

.amount-words {
    font-size: 16px;
    font-style: italic;
    margin-top: 15px;
    padding: 10px;
    border: 1px solid #000;
    background: white;
    color: #000;
}

/* ===== قسم التوقيع ===== */
.signature-section {
    margin-top: 50px;
    text-align: right;
}

.signature-section.rtl {
    text-align: left;
}

.signature-label {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 50px;
    color: #000;
}

.signature-line {
    border-bottom: 2px solid #000;
    width: 200px;
    height: 50px;
    margin: 0 auto 0 0;
}

.signature-section.rtl .signature-line {
    margin: 0 0 0 auto;
}

/* ===== أسفل الإيصال ===== */
.receipt-footer {
    border-top: 2px solid #000;
    padding: 20px;
    text-align: center;
    background: #f0f0f0;
    font-size: 12px;
    color: #000;
}

/* ===== إعدادات الطباعة ===== */
@media print {
    .receipt-body { 
        margin: 0; 
        background: white;
    }
    .receipt-container { 
        border: 2px solid #000;
        max-width: none;
    }
}

/* ===== تخصيصات إضافية - يمكنك تعديلها ===== */

/* لتغيير لون الحدود */
/*
.receipt-container,
.info-table td,
.amount-section {
    border-color: #333;
}
*/

/* لتغيير خط النص */
/*
.receipt-body {
    font-family: 'Times New Roman', serif;
}
*/

/* لتغيير خلفية الرأس */
/*
.receipt-header {
    background: #f0f0f0;
}
*/

/* لإضافة شعار */
/*
.company-logo {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    background: url('path-to-logo.png') no-repeat center;
    background-size: contain;
}
*/

/* لتغيير ألوان المبلغ */
/*
.amount-section {
    background: #e8f4fd;
    border-color: #007bff;
}

.amount-value {
    color: #007bff;
}
*/
