/**
 * Demo Data for Syndic Management System
 * This file contains sample data for testing the application
 */

const demoData = {
    owners: [
        {
            id: 'owner_1',
            fullName: '<PERSON>',
            group: 'OR2',
            building: 'A',
            apartment: '101',
            titleNumber: 'TF-12345/A',
            phone: '+212 6 12 34 56 78',
            email: '<EMAIL>',
            status: 'active',
            createdAt: '2023-01-15T10:00:00.000Z',
            updatedAt: '2023-01-15T10:00:00.000Z'
        },
        {
            id: 'owner_2',
            fullName: 'Fatima Zahra Alami',
            group: 'OR3',
            building: 'B',
            apartment: '205',
            titleNumber: 'TF-12346/B',
            phone: '+212 6 23 45 67 89',
            email: '<EMAIL>',
            status: 'active',
            createdAt: '2023-01-20T14:30:00.000Z',
            updatedAt: '2023-01-20T14:30:00.000Z'
        },
        {
            id: 'owner_3',
            fullName: '<PERSON>',
            group: 'GH14',
            building: 'C',
            apartment: '302',
            titleNumber: 'TF-12347/C',
            phone: '+212 6 34 56 78 90',
            email: '<EMAIL>',
            status: 'active',
            createdAt: '2023-02-01T09:15:00.000Z',
            updatedAt: '2023-02-01T09:15:00.000Z'
        },
        {
            id: 'owner_4',
            fullName: 'Aicha Benkirane',
            group: 'OR4',
            building: 'A',
            apartment: '403',
            titleNumber: 'TF-12348/A',
            phone: '+212 6 45 67 89 01',
            email: '<EMAIL>',
            status: 'inactive',
            createdAt: '2023-02-10T16:45:00.000Z',
            updatedAt: '2023-02-10T16:45:00.000Z'
        },
        {
            id: 'owner_5',
            fullName: 'Youssef Idrissi',
            group: 'GH15',
            building: 'D',
            apartment: '501',
            titleNumber: 'TF-12349/D',
            phone: '+212 6 56 78 90 12',
            email: '<EMAIL>',
            status: 'active',
            createdAt: '2023-02-15T11:20:00.000Z',
            updatedAt: '2023-02-15T11:20:00.000Z'
        }
    ],
    
    payments: [
        {
            id: 'payment_1',
            ownerId: 'owner_1',
            amount: 1200,
            date: '2024-01-15',
            year: 2024,
            paymentMethod: 'cash',
            receiptNumber: '2024-0001',
            createdAt: '2024-01-15T10:30:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z'
        },
        {
            id: 'payment_2',
            ownerId: 'owner_2',
            amount: 1200,
            date: '2024-01-20',
            year: 2024,
            paymentMethod: 'transfer',
            receiptNumber: '2024-0002',
            createdAt: '2024-01-20T14:45:00.000Z',
            updatedAt: '2024-01-20T14:45:00.000Z'
        },
        {
            id: 'payment_3',
            ownerId: 'owner_3',
            amount: 1200,
            date: '2024-02-01',
            year: 2024,
            paymentMethod: 'cheque',
            receiptNumber: '2024-0003',
            createdAt: '2024-02-01T09:30:00.000Z',
            updatedAt: '2024-02-01T09:30:00.000Z'
        },
        {
            id: 'payment_4',
            ownerId: 'owner_1',
            amount: 1200,
            date: '2023-03-15',
            year: 2023,
            paymentMethod: 'cash',
            receiptNumber: '2023-0015',
            createdAt: '2023-03-15T10:30:00.000Z',
            updatedAt: '2023-03-15T10:30:00.000Z'
        },
        {
            id: 'payment_5',
            ownerId: 'owner_5',
            amount: 1200,
            date: '2024-01-25',
            year: 2024,
            paymentMethod: 'transfer',
            receiptNumber: '2024-0004',
            createdAt: '2024-01-25T16:15:00.000Z',
            updatedAt: '2024-01-25T16:15:00.000Z'
        }
    ],
    
    expenses: [
        {
            id: 'expense_1',
            type: 'maintenance',
            description: 'Réparation ascenseur bâtiment A',
            amount: 3500,
            date: '2024-01-10',
            invoiceFile: null,
            createdAt: '2024-01-10T08:00:00.000Z',
            updatedAt: '2024-01-10T08:00:00.000Z'
        },
        {
            id: 'expense_2',
            type: 'electricity',
            description: 'Facture électricité parties communes',
            amount: 850,
            date: '2024-01-05',
            invoiceFile: null,
            createdAt: '2024-01-05T12:00:00.000Z',
            updatedAt: '2024-01-05T12:00:00.000Z'
        },
        {
            id: 'expense_3',
            type: 'cleaning',
            description: 'Nettoyage mensuel des parties communes',
            amount: 600,
            date: '2024-01-31',
            invoiceFile: null,
            createdAt: '2024-01-31T17:00:00.000Z',
            updatedAt: '2024-01-31T17:00:00.000Z'
        },
        {
            id: 'expense_4',
            type: 'water',
            description: 'Facture eau parties communes',
            amount: 420,
            date: '2024-01-08',
            invoiceFile: null,
            createdAt: '2024-01-08T14:30:00.000Z',
            updatedAt: '2024-01-08T14:30:00.000Z'
        },
        {
            id: 'expense_5',
            type: 'security',
            description: 'Service de gardiennage - Janvier 2024',
            amount: 2800,
            date: '2024-01-31',
            invoiceFile: null,
            createdAt: '2024-01-31T18:00:00.000Z',
            updatedAt: '2024-01-31T18:00:00.000Z'
        }
    ],
    
    documents: [
        {
            id: 'document_1',
            type: 'payment_notice',
            ownerId: 'owner_4',
            description: 'Avis de paiement pour cotisation 2023',
            date: '2024-01-15',
            documentFile: null,
            createdAt: '2024-01-15T09:00:00.000Z',
            updatedAt: '2024-01-15T09:00:00.000Z'
        },
        {
            id: 'document_2',
            type: 'ownership_certificate',
            ownerId: 'owner_1',
            description: 'Certificat de propriété - Appartement A101',
            date: '2023-12-20',
            documentFile: null,
            createdAt: '2023-12-20T15:30:00.000Z',
            updatedAt: '2023-12-20T15:30:00.000Z'
        }
    ],
    
    settings: {
        receiptCounter: 5,
        currency: 'DH',
        companyName: 'Syndic Résidence Al Manar',
        address: '123 Avenue Mohammed V, Casablanca',
        phone: '+212 5 22 12 34 56',
        email: '<EMAIL>'
    }
};

/**
 * Load demo data into the application
 */
function loadDemoData() {
    if (confirm('Voulez-vous charger les données de démonstration ? Cela remplacera toutes les données existantes.')) {
        // Clear existing data
        window.storageManager.clearAllData();
        
        // Load demo data
        window.storageManager.saveOwners(demoData.owners);
        window.storageManager.savePayments(demoData.payments);
        window.storageManager.saveExpenses(demoData.expenses);
        window.storageManager.saveDocuments(demoData.documents);
        window.storageManager.saveSettings(demoData.settings);
        
        // Refresh the current view
        if (window.app) {
            window.app.loadDashboard();
            if (window.app.currentSection === 'owners') {
                window.app.loadOwners();
            } else if (window.app.currentSection === 'income') {
                window.app.loadIncome();
            }
        }
        
        // Show success notification
        if (window.app && window.app.showNotification) {
            window.app.showNotification('success', 'Données de démonstration chargées avec succès !');
        }
        
        console.log('Demo data loaded successfully!');
    }
}

/**
 * Export demo data for backup
 */
function exportDemoData() {
    const dataStr = JSON.stringify(demoData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'syndic-demo-data.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Add demo data controls to the page
document.addEventListener('DOMContentLoaded', () => {
    // Add demo data button to the page (for development)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const demoButton = document.createElement('button');
        demoButton.textContent = 'Charger Données Demo';
        demoButton.className = 'btn btn-warning';
        demoButton.style.position = 'fixed';
        demoButton.style.bottom = '20px';
        demoButton.style.left = '20px';
        demoButton.style.zIndex = '9999';
        demoButton.onclick = loadDemoData;
        document.body.appendChild(demoButton);
        
        const exportButton = document.createElement('button');
        exportButton.textContent = 'Exporter Demo';
        exportButton.className = 'btn btn-success';
        exportButton.style.position = 'fixed';
        exportButton.style.bottom = '70px';
        exportButton.style.left = '20px';
        exportButton.style.zIndex = '9999';
        exportButton.onclick = exportDemoData;
        document.body.appendChild(exportButton);
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { demoData, loadDemoData, exportDemoData };
}
