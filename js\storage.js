/**
 * Local Storage Management System
 * Handles all data persistence for the Syndic Management App
 */

class StorageManager {
    constructor() {
        this.storageKeys = {
            owners: 'syndic_owners',
            payments: 'syndic_payments',
            expenses: 'syndic_expenses',
            documents: 'syndic_documents',
            settings: 'syndic_settings',
            receipts: 'syndic_receipts'
        };
        this.init();
    }
    
    init() {
        // Initialize storage with default data if empty
        this.initializeDefaultData();
    }
    
    initializeDefaultData() {
        // Initialize owners if empty
        if (!this.getOwners().length) {
            this.saveOwners([]);
        }
        
        // Initialize payments if empty
        if (!this.getPayments().length) {
            this.savePayments([]);
        }
        
        // Initialize expenses if empty
        if (!this.getExpenses().length) {
            this.saveExpenses([]);
        }
        
        // Initialize documents if empty
        if (!this.getDocuments().length) {
            this.saveDocuments([]);
        }
        
        // Initialize settings
        if (!this.getSettings()) {
            this.saveSettings({
                receiptCounter: 1,
                currency: 'DH',
                companyName: 'Syndic de Copropriété',
                address: '',
                phone: '',
                email: ''
            });
        }
    }
    
    // Generic storage methods
    setItem(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }
    
    getItem(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }
    
    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }
    
    // Owners management
    getOwners() {
        return this.getItem(this.storageKeys.owners) || [];
    }
    
    saveOwners(owners) {
        return this.setItem(this.storageKeys.owners, owners);
    }
    
    addOwner(owner) {
        const owners = this.getOwners();
        owner.id = this.generateId();
        owner.createdAt = new Date().toISOString();
        owner.updatedAt = new Date().toISOString();
        owners.push(owner);
        return this.saveOwners(owners) ? owner : null;
    }
    
    updateOwner(ownerId, updatedOwner) {
        const owners = this.getOwners();
        const index = owners.findIndex(owner => owner.id === ownerId);
        if (index !== -1) {
            owners[index] = { ...owners[index], ...updatedOwner, updatedAt: new Date().toISOString() };
            return this.saveOwners(owners) ? owners[index] : null;
        }
        return null;
    }
    
    deleteOwner(ownerId) {
        const owners = this.getOwners();
        const filteredOwners = owners.filter(owner => owner.id !== ownerId);
        return this.saveOwners(filteredOwners);
    }
    
    getOwnerById(ownerId) {
        const owners = this.getOwners();
        return owners.find(owner => owner.id === ownerId) || null;
    }
    
    // Payments management
    getPayments() {
        return this.getItem(this.storageKeys.payments) || [];
    }
    
    savePayments(payments) {
        return this.setItem(this.storageKeys.payments, payments);
    }
    
    addPayment(payment) {
        const payments = this.getPayments();
        payment.id = this.generateId();
        payment.createdAt = new Date().toISOString();
        payment.updatedAt = new Date().toISOString();
        
        // Generate receipt number
        const settings = this.getSettings();
        payment.receiptNumber = this.generateReceiptNumber();
        settings.receiptCounter++;
        this.saveSettings(settings);
        
        payments.push(payment);
        return this.savePayments(payments) ? payment : null;
    }
    
    updatePayment(paymentId, updatedPayment) {
        const payments = this.getPayments();
        const index = payments.findIndex(payment => payment.id === paymentId);
        if (index !== -1) {
            payments[index] = { ...payments[index], ...updatedPayment, updatedAt: new Date().toISOString() };
            return this.savePayments(payments) ? payments[index] : null;
        }
        return null;
    }
    
    deletePayment(paymentId) {
        const payments = this.getPayments();
        const filteredPayments = payments.filter(payment => payment.id !== paymentId);
        return this.savePayments(filteredPayments);
    }
    
    getPaymentById(paymentId) {
        const payments = this.getPayments();
        return payments.find(payment => payment.id === paymentId) || null;
    }
    
    getPaymentsByOwner(ownerId) {
        const payments = this.getPayments();
        return payments.filter(payment => payment.ownerId === ownerId);
    }
    
    getPaymentsByYear(year) {
        const payments = this.getPayments();
        return payments.filter(payment => payment.year === year);
    }
    
    // Expenses management
    getExpenses() {
        return this.getItem(this.storageKeys.expenses) || [];
    }
    
    saveExpenses(expenses) {
        return this.setItem(this.storageKeys.expenses, expenses);
    }
    
    addExpense(expense) {
        const expenses = this.getExpenses();
        expense.id = this.generateId();
        expense.createdAt = new Date().toISOString();
        expense.updatedAt = new Date().toISOString();
        expenses.push(expense);
        return this.saveExpenses(expenses) ? expense : null;
    }
    
    updateExpense(expenseId, updatedExpense) {
        const expenses = this.getExpenses();
        const index = expenses.findIndex(expense => expense.id === expenseId);
        if (index !== -1) {
            expenses[index] = { ...expenses[index], ...updatedExpense, updatedAt: new Date().toISOString() };
            return this.saveExpenses(expenses) ? expenses[index] : null;
        }
        return null;
    }
    
    deleteExpense(expenseId) {
        const expenses = this.getExpenses();
        const filteredExpenses = expenses.filter(expense => expense.id !== expenseId);
        return this.saveExpenses(filteredExpenses);
    }
    
    getExpenseById(expenseId) {
        const expenses = this.getExpenses();
        return expenses.find(expense => expense.id === expenseId) || null;
    }
    
    // Documents management
    getDocuments() {
        return this.getItem(this.storageKeys.documents) || [];
    }
    
    saveDocuments(documents) {
        return this.setItem(this.storageKeys.documents, documents);
    }
    
    addDocument(document) {
        const documents = this.getDocuments();
        document.id = this.generateId();
        document.createdAt = new Date().toISOString();
        document.updatedAt = new Date().toISOString();
        documents.push(document);
        return this.saveDocuments(documents) ? document : null;
    }
    
    updateDocument(documentId, updatedDocument) {
        const documents = this.getDocuments();
        const index = documents.findIndex(doc => doc.id === documentId);
        if (index !== -1) {
            documents[index] = { ...documents[index], ...updatedDocument, updatedAt: new Date().toISOString() };
            return this.saveDocuments(documents) ? documents[index] : null;
        }
        return null;
    }
    
    deleteDocument(documentId) {
        const documents = this.getDocuments();
        const filteredDocuments = documents.filter(doc => doc.id !== documentId);
        return this.saveDocuments(filteredDocuments);
    }
    
    getDocumentById(documentId) {
        const documents = this.getDocuments();
        return documents.find(doc => doc.id === documentId) || null;
    }
    
    // Settings management
    getSettings() {
        return this.getItem(this.storageKeys.settings) || {};
    }
    
    saveSettings(settings) {
        return this.setItem(this.storageKeys.settings, settings);
    }
    
    updateSetting(key, value) {
        const settings = this.getSettings();
        settings[key] = value;
        return this.saveSettings(settings);
    }
    
    // Utility methods
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    generateReceiptNumber() {
        const settings = this.getSettings();
        const year = new Date().getFullYear();
        const counter = settings.receiptCounter || 1;
        return `${year}-${counter.toString().padStart(4, '0')}`;
    }
    
    // Statistics and calculations
    getTotalIncome(year = null) {
        const payments = year ? this.getPaymentsByYear(year) : this.getPayments();
        return payments.reduce((total, payment) => total + (payment.amount || 0), 0);
    }
    
    getTotalExpenses(year = null) {
        const expenses = year ? this.getExpenses().filter(exp => new Date(exp.date).getFullYear() === year) : this.getExpenses();
        return expenses.reduce((total, expense) => total + (expense.amount || 0), 0);
    }
    
    getCurrentBalance(year = null) {
        return this.getTotalIncome(year) - this.getTotalExpenses(year);
    }
    
    getOverduePayments() {
        const owners = this.getOwners();
        const payments = this.getPayments();
        const currentYear = new Date().getFullYear();
        const overduePayments = [];
        
        owners.forEach(owner => {
            const ownerPayments = payments.filter(p => p.ownerId === owner.id);
            const paidYears = ownerPayments.map(p => p.year);
            
            // Check for missing payments from 2018 to current year
            for (let year = 2018; year <= currentYear; year++) {
                if (!paidYears.includes(year)) {
                    overduePayments.push({
                        owner: owner,
                        year: year,
                        daysOverdue: year < currentYear ? (currentYear - year) * 365 : 0
                    });
                }
            }
        });
        
        return overduePayments;
    }
    
    // Data export/import
    exportData() {
        return {
            owners: this.getOwners(),
            payments: this.getPayments(),
            expenses: this.getExpenses(),
            documents: this.getDocuments(),
            settings: this.getSettings(),
            exportDate: new Date().toISOString()
        };
    }
    
    importData(data) {
        try {
            if (data.owners) this.saveOwners(data.owners);
            if (data.payments) this.savePayments(data.payments);
            if (data.expenses) this.saveExpenses(data.expenses);
            if (data.documents) this.saveDocuments(data.documents);
            if (data.settings) this.saveSettings(data.settings);
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }
    
    // Clear all data (with confirmation)
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            this.removeItem(key);
        });
        this.initializeDefaultData();
    }
}

// Initialize storage manager
window.storageManager = new StorageManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageManager;
}
