/**
 * Multilingual Support System
 * Supports French and Arabic with RTL layout
 */

const translations = {
    fr: {
        // App General
        app_title: "Gestion Syndic",
        loading: "Chargement...",
        
        // Navigation
        dashboard: "Tableau de bord",
        owners: "Propriétaires",
        income: "Recettes",
        expenses: "Dépenses",
        reports: "Rapports",
        legal: "Documents",
        
        // Dashboard
        dashboard_subtitle: "Vue d'ensemble de votre syndic",
        total_owners: "Total Propriétaires",
        total_income: "Recettes Totales",
        total_expenses: "Dépenses Totales",
        current_balance: "Solde Actuel",
        recent_payments: "Paiements Récents",
        overdue_payments: "Paiements en Retard",
        
        // Owners Management
        owners_management: "Gestion des Propriétaires",
        add_owner: "Ajouter Propriétaire",
        edit_owner: "Modifier Propriétaire",
        delete_owner: "Supprimer Propriétaire",
        owner_details: "Détails du Propriétaire",
        full_name: "Nom Complet",
        group: "Groupe",
        building: "Bâtiment",
        apartment: "Appartement",
        title_number: "N° Titre (T.F)",
        phone: "Téléphone",
        email: "Email",
        status: "Statut",
        active: "Actif",
        inactive: "Inactif",
        actions: "Actions",
        search_owners: "Rechercher propriétaires...",
        all_groups: "Tous les groupes",
        filter_by_group: "Filtrer par groupe",
        filter_by_date: "Filtrer par date",
        filter_by_year: "Filtrer par année",
        filter_by_type: "Filtrer par type",
        all: "Tous",
        
        // Income Management
        income_management: "Gestion des Recettes",
        add_payment: "Enregistrer Paiement",
        edit_payment: "Modifier Paiement",
        payment_details: "Détails du Paiement",
        receipt_number: "N° Reçu",
        date: "Date",
        owner: "Propriétaire",
        amount: "Montant",
        amount_words: "Montant en lettres",
        payment_method: "Mode de Paiement",
        cash: "Espèces",
        transfer: "Virement",
        cheque: "Chèque",
        year: "Année",
        contribution_type: "Type de Cotisation",
        monthly: "Mensuelle",
        annual: "Annuelle",
        search_payments: "Rechercher paiements...",
        all_years: "Toutes les années",
        generate_receipt: "Générer Reçu",
        print_receipt: "Imprimer Reçu",
        
        // Expenses Management
        expenses_management: "Gestion des Dépenses",
        add_expense: "Ajouter Dépense",
        edit_expense: "Modifier Dépense",
        expense_details: "Détails de la Dépense",
        expense_type: "Type de Dépense",
        maintenance: "Maintenance",
        electricity: "Électricité",
        water: "Eau",
        security: "Sécurité",
        cleaning: "Nettoyage",
        other: "Autre",
        description: "Description",
        invoice_file: "Fichier Facture",
        attach_file: "Joindre Fichier",
        
        // Reports
        reports_management: "Gestion des Rapports",
        generate_report: "Générer Rapport",
        owner_statement: "Relevé Propriétaire",
        payment_report: "Rapport Paiements",
        expense_report: "Rapport Dépenses",
        balance_report: "Rapport Soldes",
        overdue_report: "Rapport Retards",
        maintenance_report: "Rapport Maintenance",
        daily_report: "Rapport Quotidien",
        monthly_report: "Rapport Mensuel",
        annual_report: "Rapport Annuel",
        export_pdf: "Exporter PDF",
        export_excel: "Exporter Excel",
        from_date: "Date de début",
        to_date: "Date de fin",

        // Report descriptions
        owner_statement_desc: "Générer le relevé de compte d'un propriétaire",
        payment_report_desc: "Rapport détaillé des paiements",
        expense_report_desc: "Analyse des dépenses par période",
        balance_report_desc: "État des soldes par année",
        overdue_report_desc: "Liste des paiements en retard",
        maintenance_report_desc: "Récit des travaux effectués",
        
        // Legal Documents
        legal_management: "Gestion Documents Légaux",
        add_document: "Ajouter Document",
        document_type: "Type de Document",
        payment_notice: "Avis de Paiement",
        ownership_certificate: "Certificat de Propriété",
        court_order: "Ordonnance Tribunal",
        seizure_order: "Ordonnance Saisie",
        judgment_registration: "Enregistrement Jugement",
        document_date: "Date Document",
        document_file: "Fichier Document",
        
        // Common Actions
        save: "Enregistrer",
        cancel: "Annuler",
        edit: "Modifier",
        delete: "Supprimer",
        view: "Voir",
        close: "Fermer",
        confirm: "Confirmer",
        yes: "Oui",
        no: "Non",
        search: "Rechercher",
        filter: "Filtrer",
        clear: "Effacer",
        select: "Sélectionner",
        
        // Messages
        confirm_delete: "Êtes-vous sûr de vouloir supprimer cet élément ?",
        delete_success: "Élément supprimé avec succès",
        save_success: "Enregistré avec succès",
        error_occurred: "Une erreur s'est produite",
        required_field: "Ce champ est obligatoire",
        invalid_email: "Email invalide",
        invalid_phone: "Numéro de téléphone invalide",
        
        // Notifications
        payment_overdue: "Paiement en retard",
        assembly_reminder: "Rappel Assemblée Générale",
        new_payment: "Nouveau paiement reçu",
        
        // Receipt Template
        receipt_title: "REÇU DE PAIEMENT",
        receipt_subtitle: "Syndic de Copropriété",
        received_from: "Reçu de",
        sum_of: "La somme de",
        dirhams: "Dirhams",
        for_year: "Pour l'année",
        payment_date: "Date de paiement",
        signature: "Signature",
        property: "Propriété",
        receipt_footer: "Merci pour votre confiance"
    },
    
    ar: {
        // App General
        app_title: "إدارة النقابة",
        loading: "جاري التحميل...",
        
        // Navigation
        dashboard: "لوحة التحكم",
        owners: "الملاك",
        income: "الإيرادات",
        expenses: "المصروفات",
        reports: "التقارير",
        legal: "الوثائق",
        
        // Dashboard
        dashboard_subtitle: "نظرة عامة على نقابتك",
        total_owners: "إجمالي الملاك",
        total_income: "إجمالي الإيرادات",
        total_expenses: "إجمالي المصروفات",
        current_balance: "الرصيد الحالي",
        recent_payments: "المدفوعات الأخيرة",
        overdue_payments: "المدفوعات المتأخرة",
        
        // Owners Management
        owners_management: "إدارة الملاك",
        add_owner: "إضافة مالك",
        edit_owner: "تعديل مالك",
        delete_owner: "حذف مالك",
        owner_details: "تفاصيل المالك",
        full_name: "الاسم الكامل",
        group: "المجموعة",
        building: "المبنى",
        apartment: "الشقة",
        title_number: "رقم السند",
        phone: "الهاتف",
        email: "البريد الإلكتروني",
        status: "الحالة",
        active: "نشط",
        inactive: "غير نشط",
        actions: "الإجراءات",
        search_owners: "البحث عن الملاك...",
        all_groups: "جميع المجموعات",
        filter_by_group: "تصفية حسب المجموعة",
        filter_by_date: "تصفية حسب التاريخ",
        filter_by_year: "تصفية حسب السنة",
        filter_by_type: "تصفية حسب النوع",
        all: "الكل",
        
        // Income Management
        income_management: "إدارة الإيرادات",
        add_payment: "تسجيل دفعة",
        edit_payment: "تعديل دفعة",
        payment_details: "تفاصيل الدفعة",
        receipt_number: "رقم الإيصال",
        date: "التاريخ",
        owner: "المالك",
        amount: "المبلغ",
        amount_words: "المبلغ بالحروف",
        payment_method: "طريقة الدفع",
        cash: "نقداً",
        transfer: "تحويل",
        cheque: "شيك",
        year: "السنة",
        contribution_type: "نوع المساهمة",
        monthly: "شهرية",
        annual: "سنوية",
        search_payments: "البحث عن المدفوعات...",
        all_years: "جميع السنوات",
        generate_receipt: "إنشاء إيصال",
        print_receipt: "طباعة إيصال",
        
        // Expenses Management
        expenses_management: "إدارة المصروفات",
        add_expense: "إضافة مصروف",
        edit_expense: "تعديل مصروف",
        expense_details: "تفاصيل المصروف",
        expense_type: "نوع المصروف",
        maintenance: "صيانة",
        electricity: "كهرباء",
        water: "ماء",
        security: "أمن",
        cleaning: "تنظيف",
        other: "أخرى",
        description: "الوصف",
        invoice_file: "ملف الفاتورة",
        attach_file: "إرفاق ملف",
        
        // Reports
        reports_management: "إدارة التقارير",
        generate_report: "إنشاء تقرير",
        owner_statement: "كشف حساب مالك",
        payment_report: "تقرير المدفوعات",
        expense_report: "تقرير المصروفات",
        balance_report: "تقرير الأرصدة",
        overdue_report: "تقرير المتأخرات",
        maintenance_report: "تقرير الصيانة",
        daily_report: "تقرير يومي",
        monthly_report: "تقرير شهري",
        annual_report: "تقرير سنوي",
        export_pdf: "تصدير PDF",
        export_excel: "تصدير Excel",
        from_date: "من تاريخ",
        to_date: "إلى تاريخ",

        // Report descriptions
        owner_statement_desc: "إنشاء كشف حساب مالك",
        payment_report_desc: "تقرير مفصل للمدفوعات",
        expense_report_desc: "تحليل المصروفات حسب الفترة",
        balance_report_desc: "حالة الأرصدة حسب السنة",
        overdue_report_desc: "قائمة المدفوعات المتأخرة",
        maintenance_report_desc: "سرد الأعمال المنجزة",
        
        // Legal Documents
        legal_management: "إدارة الوثائق القانونية",
        add_document: "إضافة وثيقة",
        document_type: "نوع الوثيقة",
        payment_notice: "إشعار دفع",
        ownership_certificate: "شهادة ملكية",
        court_order: "أمر محكمة",
        seizure_order: "أمر حجز",
        judgment_registration: "تسجيل حكم",
        document_date: "تاريخ الوثيقة",
        document_file: "ملف الوثيقة",
        
        // Common Actions
        save: "حفظ",
        cancel: "إلغاء",
        edit: "تعديل",
        delete: "حذف",
        view: "عرض",
        close: "إغلاق",
        confirm: "تأكيد",
        yes: "نعم",
        no: "لا",
        search: "بحث",
        filter: "تصفية",
        clear: "مسح",
        select: "اختيار",
        
        // Messages
        confirm_delete: "هل أنت متأكد من حذف هذا العنصر؟",
        delete_success: "تم حذف العنصر بنجاح",
        save_success: "تم الحفظ بنجاح",
        error_occurred: "حدث خطأ",
        required_field: "هذا الحقل مطلوب",
        invalid_email: "بريد إلكتروني غير صحيح",
        invalid_phone: "رقم هاتف غير صحيح",
        
        // Notifications
        payment_overdue: "دفعة متأخرة",
        assembly_reminder: "تذكير الجمعية العامة",
        new_payment: "دفعة جديدة مستلمة",
        
        // Receipt Template
        receipt_title: "إيصال دفع",
        receipt_subtitle: "نقابة الملكية المشتركة",
        received_from: "استلم من",
        sum_of: "مبلغ",
        dirhams: "درهم",
        for_year: "عن سنة",
        payment_date: "تاريخ الدفع",
        signature: "التوقيع",
        property: "العقار",
        receipt_footer: "شكراً لكم على ثقتكم",
        property: "العقار",
        receipt_footer: "شكراً لكم على ثقتكم"
    }
};

class LanguageManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('syndic_language') || 'fr';
        this.init();
    }
    
    init() {
        this.updateLanguage(this.currentLanguage);
        this.bindEvents();
    }
    
    bindEvents() {
        const languageToggle = document.getElementById('language-toggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
    }
    
    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'fr' ? 'ar' : 'fr';
        this.updateLanguage(newLanguage);
    }
    
    updateLanguage(language) {
        this.currentLanguage = language;
        localStorage.setItem('syndic_language', language);
        
        // Update HTML attributes
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        
        // Update language button
        const currentLangSpan = document.getElementById('current-lang');
        if (currentLangSpan) {
            currentLangSpan.textContent = language.toUpperCase();
        }
        
        // Update all translatable elements
        this.translatePage();
        
        // Apply RTL/LTR specific styles
        this.applyDirectionStyles();
    }
    
    translatePage() {
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
        
        // Update placeholders
        const placeholderElements = document.querySelectorAll('[data-translate-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-translate-placeholder');
            const translation = this.getTranslation(key);
            if (translation) {
                element.placeholder = translation;
            }
        });
    }
    
    getTranslation(key) {
        return translations[this.currentLanguage]?.[key] || translations.fr[key] || key;
    }
    
    applyDirectionStyles() {
        const body = document.body;
        if (this.currentLanguage === 'ar') {
            body.classList.add('rtl');
            body.classList.remove('ltr');
        } else {
            body.classList.add('ltr');
            body.classList.remove('rtl');
        }
    }
    
    // Utility method to get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    // Utility method to check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.languageManager = new LanguageManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { translations, LanguageManager };
}
